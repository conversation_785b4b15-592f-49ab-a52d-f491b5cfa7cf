"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { supabase } from "@/lib/supabase"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Trash2, RotateCcw, Search, AlertTriangle, RefreshCw } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import Sidebar from "@/components/Sidebar"

interface Document {
  id: string
  title: string
  content: string
  created_at: string
  deleted_at: string
  word_count?: number
}

export default function TrashPage() {
  const { user, loading: authLoading } = useAuth()
  const router = useRouter()
  const [docs, setDocs] = useState<Document[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [restoringDocId, setRestoringDocId] = useState<string | null>(null)
  const [deletingDocId, setDeletingDocId] = useState<string | null>(null)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/get-started")
    }
  }, [user, authLoading, router])

  // Fetch deleted documents
  useEffect(() => {
    if (!user || authLoading) return

    const fetchDeletedDocs = async () => {
      setLoading(true)
      try {
        const { data, error } = await supabase
          .from('documents')
          .select('id, title, content, created_at, deleted_at, word_count')
          .eq('user_id', user.id)
          .not('deleted_at', 'is', null)
          .order('deleted_at', { ascending: false })

        if (error) {
          console.error('Error fetching deleted documents:', error)
          return
        }

        setDocs(data || [])
      } catch (error) {
        console.error('Error fetching deleted documents:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDeletedDocs()
  }, [user, authLoading])

  // Filter documents based on search term
  const filteredDocs = docs.filter(doc =>
    doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.content.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Restore document from trash
  const handleRestoreDoc = async (docId: string) => {
    if (!user) return
    
    try {
      setRestoringDocId(docId)
      
      const { data, error } = await supabase.rpc('restore_document', {
        doc_id: docId,
        user_uuid: user.id
      })
      
      if (error) {
        console.error('Error restoring document:', error)
        alert('Error restoring document: ' + error.message)
        return
      }
      
      if (!data) {
        alert('Document not found or already restored')
        return
      }
      
      // Remove the document from the trash list
      setDocs(docs.filter(doc => doc.id !== docId))
      console.log('Document restored successfully')
    } catch (error) {
      console.error('Error restoring document:', error)
      alert('Error restoring document')
    } finally {
      setRestoringDocId(null)
    }
  }

  // Permanently delete document
  const handlePermanentDelete = async (docId: string) => {
    if (!user) return
    
    try {
      setDeletingDocId(docId)
      
      const { error } = await supabase
        .from('documents')
        .delete()
        .eq('id', docId)
        .eq('user_id', user.id)
        .not('deleted_at', 'is', null) // Only delete if it's already in trash
      
      if (error) {
        console.error('Error permanently deleting document:', error)
        alert('Error permanently deleting document: ' + error.message)
        return
      }
      
      // Remove the document from the trash list
      setDocs(docs.filter(doc => doc.id !== docId))
      console.log('Document permanently deleted')
    } catch (error) {
      console.error('Error permanently deleting document:', error)
      alert('Error permanently deleting document')
    } finally {
      setDeletingDocId(null)
    }
  }

  // Empty entire trash
  const handleEmptyTrash = async () => {
    if (!user || docs.length === 0) return
    
    if (!confirm(`Are you sure you want to permanently delete all ${docs.length} documents in trash? This action cannot be undone.`)) {
      return
    }
    
    try {
      setLoading(true)
      
      const { error } = await supabase
        .from('documents')
        .delete()
        .eq('user_id', user.id)
        .not('deleted_at', 'is', null)
      
      if (error) {
        console.error('Error emptying trash:', error)
        alert('Error emptying trash: ' + error.message)
        return
      }
      
      setDocs([])
      console.log('Trash emptied successfully')
    } catch (error) {
      console.error('Error emptying trash:', error)
      alert('Error emptying trash')
    } finally {
      setLoading(false)
    }
  }

  if (authLoading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-amber-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 flex">
      <Sidebar />
      
      <main className="flex-1 p-8 ml-64">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-amber-900 mb-2">Trash</h1>
              <p className="text-amber-700">
                Documents in trash are automatically deleted after 30 days
              </p>
            </div>
            
            {docs.length > 0 && (
              <Button
                onClick={handleEmptyTrash}
                variant="destructive"
                className="bg-red-600 hover:bg-red-700"
                disabled={loading}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Empty Trash
              </Button>
            )}
          </div>

          {/* Search */}
          <div className="relative mb-6">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-500 h-4 w-4" />
            <Input
              placeholder="Search deleted documents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 border-amber-200 focus:border-amber-400 focus:ring-amber-400"
            />
          </div>

          {/* Documents Grid */}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
            </div>
          ) : docs.length === 0 ? (
            <div className="text-center py-12">
              <Trash2 className="h-16 w-16 text-amber-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-amber-900 mb-2">Trash is empty</h3>
              <p className="text-amber-600">Deleted documents will appear here</p>
            </div>
          ) : filteredDocs.length === 0 ? (
            <div className="text-center py-12">
              <Search className="h-16 w-16 text-amber-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-amber-900 mb-2">No documents found</h3>
              <p className="text-amber-600">Try adjusting your search terms</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredDocs.map(doc => (
                <Card 
                  key={doc.id} 
                  className="border-amber-200 bg-white/90 hover:bg-white hover:shadow-xl transition-all duration-300"
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-red-500 font-medium">
                        Deleted {new Date(doc.deleted_at).toLocaleDateString('en-US', { 
                          month: 'short', 
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </span>
                      <span className="text-xs text-amber-600 border border-amber-300 rounded-full px-2 py-0.5">
                        {doc.word_count || 0} words
                      </span>
                    </div>
                    <CardTitle className="text-amber-900 text-lg truncate">{doc.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-amber-700 text-sm mb-4 line-clamp-3">
                      {doc.content ? doc.content.substring(0, 150) + (doc.content.length > 150 ? '...' : '') : 'No content'}
                    </p>
                    
                    <div className="flex items-center justify-between gap-2">
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="text-amber-600 border-amber-300 hover:bg-amber-50 flex-1"
                        disabled={restoringDocId === doc.id}
                        onClick={() => handleRestoreDoc(doc.id)}
                      >
                        {restoringDocId === doc.id ? (
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <RotateCcw className="h-4 w-4 mr-2" />
                        )}
                        Restore
                      </Button>
                      
                      <Button 
                        size="sm" 
                        variant="destructive"
                        className="bg-red-600 hover:bg-red-700"
                        disabled={deletingDocId === doc.id}
                        onClick={() => {
                          if (confirm('Are you sure you want to permanently delete this document? This action cannot be undone.')) {
                            handlePermanentDelete(doc.id)
                          }
                        }}
                      >
                        {deletingDocId === doc.id ? (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
