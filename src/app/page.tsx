"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { BookOpen, FileText, Zap, Check, ArrowRight, Users, Clock, Shield, User, LogOut } from "lucide-react"
import Link from "next/link"
import { useAuth } from "@/contexts/AuthContext"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { PricingPlans } from '@/components/PricingPlans';

export default function CiteAILanding() {
  const { user, signOut } = useAuth()
  const router = useRouter()
  const [signOutLoading, setSignOutLoading] = useState(false)

  const handleSignOut = async () => {
    try {
      setSignOutLoading(true)
      console.log('Landing: Signing out...')
      await signOut()
      console.log('Landing: Sign out successful, redirecting...')
      router.push("/get-started?signin=1")
    } catch (error) {
      console.error("Landing: Error signing out:", error)
      // Still redirect even if there's an error
      router.push("/get-started?signin=1")
    } finally {
      setSignOutLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-amber-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-8 w-8 text-amber-600" />
              <span className="text-2xl font-bold text-amber-900">CiteAI</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link href="#how-it-works" className="text-amber-800 hover:text-amber-600 transition-colors">
                How it Works
              </Link>
              <Link href="#pricing" className="text-amber-800 hover:text-amber-600 transition-colors">
                Pricing
              </Link>
              <Link href="#faq" className="text-amber-800 hover:text-amber-600 transition-colors">
                FAQ
              </Link>
              {user ? (
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 text-amber-800">
                    <User className="h-4 w-4" />
                    <span className="text-sm">{user.email}</span>
                  </div>
                  <Link href="/dashboard">
                    <Button className="bg-amber-600 hover:bg-amber-700 text-white">Dashboard</Button>
                  </Link>
                  <Button
                    onClick={handleSignOut}
                    variant="outline"
                    size="sm"
                    className="border-amber-300 text-amber-800 hover:bg-amber-50"
                    disabled={signOutLoading}
                  >
                    {signOutLoading ? (
                      <svg className="animate-spin h-4 w-4 text-amber-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      <>
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                      </>
                    )}
                  </Button>
                </div>
              ) : (
                <Link href="/get-started">
                  <Button className="bg-amber-600 hover:bg-amber-700 text-white">Get Started</Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 lg:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <Badge className="mb-6 bg-amber-100 text-amber-800 border-amber-300">AI-Powered Citation Generator</Badge>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-amber-900 mb-6 leading-tight">
              Perfect Citations,
              <span className="text-amber-600"> Effortlessly</span>
            </h1>
            <p className="text-xl md:text-2xl text-amber-700 mb-8 leading-relaxed">
              Transform your essays into perfectly formatted works cited pages in seconds. CiteAI automatically detects
              sources and generates accurate citations in any format.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              {user ? (
                <Link href="/dashboard">
                  <Button size="lg" className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-4 text-lg">
                    Go to Dashboard
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              ) : (
                <Link href="/get-started">
                  <Button size="lg" className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-4 text-lg">
                    Start Citing Now
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              )}
              <Button
                size="lg"
                variant="outline"
                className="border-amber-300 text-amber-800 hover:bg-amber-50 px-8 py-4 text-lg bg-transparent"
              >
                Watch Demo
              </Button>
            </div>
            <div className="mt-12 flex justify-center items-center space-x-8 text-amber-600">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span className="text-sm">10,000+ Students</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <span className="text-sm">Save 2+ Hours</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span className="text-sm">99.9% Accurate</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="py-20 bg-white/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-amber-900 mb-4">How CiteAI Works</h2>
            <p className="text-xl text-amber-700 max-w-2xl mx-auto">
              Three simple steps to perfect citations. No more manual formatting or style guide confusion.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <Card className="text-center border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50 hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FileText className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-amber-900">1. Paste Your Essay</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-amber-700">
                  Simply copy and paste your essay or research paper into our intelligent text analyzer.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50 hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-amber-900">2. Hit Cite</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-amber-700">
                  Our AI instantly scans your text, identifies all sources, and prepares them for citation.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50 hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-amber-900">3. Get Your Citations</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-amber-700">
                  Receive a perfectly formatted works cited page in MLA, APA, Chicago, or any style you need.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section id="pricing" className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-amber-900 mb-4">Simple, Transparent Pricing</h2>
            <p className="text-xl text-amber-700 max-w-2xl mx-auto">
              Choose the plan that fits your academic needs. All plans include unlimited citations.
            </p>
          </div>
          <PricingPlans />
        </div>
      </section>

      {/* FAQ */}
      <section id="faq" className="py-20 bg-white/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-amber-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-amber-700 max-w-2xl mx-auto">
              Everything you need to know about CiteAI and how it works.
            </p>
          </div>

          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible className="space-y-4">
              <AccordionItem value="item-1" className="bg-white/70 rounded-lg border border-amber-200 px-6">
                <AccordionTrigger className="text-amber-900 hover:text-amber-700">
                  How accurate are the citations generated by CiteAI?
                </AccordionTrigger>
                <AccordionContent className="text-amber-700">
                  CiteAI uses advanced AI algorithms trained on millions of academic sources to ensure 99.9% accuracy.
                  Our system is regularly updated with the latest citation style guidelines from MLA, APA, Chicago, and
                  other major formats.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-2" className="bg-white/70 rounded-lg border border-amber-200 px-6">
                <AccordionTrigger className="text-amber-900 hover:text-amber-700">
                  What citation formats does CiteAI support?
                </AccordionTrigger>
                <AccordionContent className="text-amber-700">
                  CiteAI supports all major citation formats including MLA, APA, Chicago, Harvard, IEEE, and many more.
                  We're constantly adding new formats based on user requests and academic requirements.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-3" className="bg-white/70 rounded-lg border border-amber-200 px-6">
                <AccordionTrigger className="text-amber-900 hover:text-amber-700">
                  Is my essay content secure and private?
                </AccordionTrigger>
                <AccordionContent className="text-amber-700">
                  Absolutely. We use enterprise-grade encryption to protect your content. Your essays are processed
                  securely and are never stored permanently on our servers. We're committed to maintaining your academic
                  privacy and integrity.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-4" className="bg-white/70 rounded-lg border border-amber-200 px-6">
                <AccordionTrigger className="text-amber-900 hover:text-amber-700">
                  Can I edit the citations after they're generated?
                </AccordionTrigger>
                <AccordionContent className="text-amber-700">
                  Yes! While our AI generates highly accurate citations, you can always review and edit them as needed.
                  Our interface makes it easy to modify any citation details while maintaining proper formatting.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-5" className="bg-white/70 rounded-lg border border-amber-200 px-6">
                <AccordionTrigger className="text-amber-900 hover:text-amber-700">
                  Do you offer student discounts?
                </AccordionTrigger>
                <AccordionContent className="text-amber-700">
                  Yes! We offer special pricing for students with valid .edu email addresses. Contact our support team
                  with your student credentials to learn about available discounts.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-amber-600 to-orange-600 rounded-2xl p-12 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Transform Your Citation Process?</h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Join thousands of students and researchers who save hours every week with CiteAI. Start your free trial
              today and experience the future of academic writing.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center space-x-4">
                <Input
                  type="email"
                  placeholder="Enter your email address"
                  className="bg-white/20 border-white/30 text-white placeholder:text-white/70 w-80"
                />
                <Link href="/get-started">
                  <Button size="lg" className="bg-white text-amber-600 hover:bg-gray-100">
                    Get Started Free
                  </Button>
                </Link>
              </div>
            </div>
            <p className="text-sm mt-4 opacity-75">No credit card required • 5 free citations • Cancel anytime</p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-amber-900 text-amber-100 py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <BookOpen className="h-6 w-6" />
                <span className="text-xl font-bold">CiteAI</span>
              </div>
              <p className="text-amber-200 text-sm">
                The intelligent citation generator that saves you time and ensures accuracy in your academic work.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    Features
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    Pricing
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    API
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    Integrations
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    Status
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    Community
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    About
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    Blog
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    Careers
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-amber-200 hover:text-white transition-colors">
                    Privacy
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-amber-800 mt-8 pt-8 text-center text-sm text-amber-200">
            <p>&copy; {new Date().getFullYear()} CiteAI. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
