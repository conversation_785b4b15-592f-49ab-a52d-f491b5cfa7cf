"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  BookOpen,
  User,
  CreditCard,
  Shield,
  Bell,
  Download,
  Trash2,
  Edit3,
  Crown,
  FileText,
  Settings,
  Home,
  LogOut,
} from "lucide-react"
import Link from "next/link"
import { useAuth } from "@/contexts/AuthContext"
import Sidebar from "@/components/Sidebar"
import { usePlan } from '@/contexts/PlanContext';
import { PricingPlans } from '@/components/PricingPlans';

export default function AccountPage() {
  const { user, signOut } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [preferences, setPreferences] = useState({
    emailNotifications: true,
    mailingList: true,
    productUpdates: false,
    marketingEmails: false,
    twoFactorAuth: false,
  })
  const [profile, setProfile] = useState({
    firstName: user?.user_metadata?.first_name || user?.user_metadata?.name?.split(' ')[0] || '',
    lastName: user?.user_metadata?.last_name || user?.user_metadata?.name?.split(' ').slice(1).join(' ') || '',
    email: user?.email || '',
    avatar: user?.user_metadata?.avatar_url || '',
    joinDate: "March 2024",
  })
  const { currentPlan } = usePlan();
  const planColor = currentPlan === 'plus' ? 'bg-amber-600 text-white' : currentPlan === 'pro' ? 'bg-amber-400 text-amber-900' : 'bg-amber-900 text-white';
  const planLabel = currentPlan === 'plus' ? 'Plus Plan' : currentPlan === 'pro' ? 'Pro Plan' : 'Free Plan';
  const planPrice = currentPlan === 'plus' ? '$15/monthly' : currentPlan === 'pro' ? '$30/monthly' : '$0/monthly';
  const [showPricing, setShowPricing] = useState(false);

  const handlePreferenceChange = (key: string, value: boolean) => {
    setPreferences((prev) => ({ ...prev, [key]: value }))
  }

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50">
      <Sidebar user={user} signOut={signOut} />
      <div className="ml-64 flex-1 p-6">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <div>
            <h1 className="text-4xl font-bold text-amber-900 mb-2">Account Settings</h1>
            <p className="text-xl text-amber-700">Manage your profile, subscription, and preferences</p>
          </div>

          {/* Profile Information */}
          <Card className="border-amber-200 bg-white/70">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-amber-900 text-2xl">Profile Information</CardTitle>
                <CardDescription className="text-amber-700">Update your personal details and profile picture</CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(!isEditing)}
                className="border-amber-300 text-amber-800 hover:bg-amber-50"
              >
                <Edit3 className="w-4 h-4 mr-2" />
                {isEditing ? "Cancel" : "Edit"}
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <Avatar className="w-20 h-20">
                  <AvatarImage src={profile.avatar || "/placeholder.svg"} />
                  <AvatarFallback className="bg-amber-200 text-amber-800 text-xl">
                    {profile.firstName[0]}
                    {profile.lastName[0]}
                  </AvatarFallback>
                </Avatar>
                {isEditing && (
                  <Button
                    variant="outline"
                    className="border-amber-300 text-amber-800 hover:bg-amber-50 bg-transparent"
                  >
                    Change Photo
                  </Button>
                )}
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-amber-800">First Name</Label>
                  <Input
                    id="firstName"
                    value={profile.firstName}
                    disabled={!isEditing}
                    className="border-amber-200 focus:border-amber-500 disabled:bg-amber-50"
                    onChange={e => setProfile({ ...profile, firstName: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-amber-800">Last Name</Label>
                  <Input
                    id="lastName"
                    value={profile.lastName}
                    disabled={!isEditing}
                    className="border-amber-200 focus:border-amber-500 disabled:bg-amber-50"
                    onChange={e => setProfile({ ...profile, lastName: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email" className="text-amber-800">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={profile.email}
                  disabled={!isEditing}
                  className="border-amber-200 focus:border-amber-500 disabled:bg-amber-50"
                  onChange={e => setProfile({ ...profile, email: e.target.value })}
                />
              </div>
              <div className="flex items-center justify-between text-sm text-amber-600">
                <span>Member since {profile.joinDate}</span>
                {isEditing && (
                  <Button className="bg-amber-600 hover:bg-amber-700 text-white">Save Changes</Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Subscription Plan */}
          <Card className="border-amber-200 bg-white/70">
            <CardHeader>
              <CardTitle className="text-amber-900 flex items-center text-2xl">
                <Crown className="w-6 h-6 mr-2 text-amber-600" /> Subscription Plan
              </CardTitle>
              <CardDescription className="text-amber-700">Manage your subscription and billing</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-amber-50 rounded-lg border border-amber-200">
                <div>
                  <div className="flex items-center space-x-2">
                    <h3 className="font-semibold text-amber-900 text-lg">{planLabel}</h3>
                    <Badge className={planColor + " text-base px-3 py-1 rounded-full font-semibold"}>Active</Badge>
                  </div>
                  <p className="text-sm text-amber-700">{planPrice}</p>
                  <p className="text-xs text-amber-600">Next billing: April 15, 2024</p>
                </div>
                <div className="space-x-2">
                  <Button variant="outline" className="border-amber-300 text-amber-800 hover:bg-amber-50 bg-transparent" onClick={() => setShowPricing(true)}>Change Plan</Button>
                  <Button variant="outline" className="border-red-300 text-red-600 hover:bg-red-50 bg-transparent">Cancel</Button>
                </div>
              </div>
              {/* Pricing Modal/Section - styled like landing page pricing */}
              {showPricing && (
                <PricingPlans modal onClose={() => setShowPricing(false)} />
              )}
            </CardContent>
          </Card>

          {/* Payment Method */}
          <Card className="border-amber-200 bg-white/70">
            <CardHeader>
              <CardTitle className="text-amber-900 flex items-center text-2xl">
                <CreditCard className="w-6 h-6 mr-2 text-amber-600" /> Payment Method
              </CardTitle>
              <CardDescription className="text-amber-700">Manage your payment information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-amber-50 rounded-lg border border-amber-200">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-6 bg-blue-600 rounded flex items-center justify-center">
                    <span className="text-white text-xs font-bold">VISA</span>
                  </div>
                  <div>
                    <p className="font-medium text-amber-900">**** **** **** 4242</p>
                    <p className="text-sm text-amber-600">Expires 12/27</p>
                  </div>
                </div>
                <Button variant="outline" className="border-amber-300 text-amber-800 hover:bg-amber-50 bg-transparent">Change Payment Method</Button>
              </div>
              <div className="flex items-center space-x-4 text-sm text-amber-600">
                <div className="flex items-center space-x-2">
                  <Shield className="w-4 h-4" />
                  <span>Secured by 256-bit SSL encryption</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notifications & Preferences */}
          <Card className="border-amber-200 bg-white/70">
            <CardHeader>
              <CardTitle className="text-amber-900 flex items-center text-2xl">
                <Bell className="w-6 h-6 mr-2 text-amber-600" /> Notifications & Preferences
              </CardTitle>
              <CardDescription className="text-amber-700">Control how you receive updates and communications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-amber-900">Email Notifications</p>
                    <p className="text-sm text-amber-600">Receive notifications about your citations and documents</p>
                  </div>
                  <Switch checked={preferences.emailNotifications} onCheckedChange={checked => handlePreferenceChange("emailNotifications", checked)} />
                </div>
                <Separator className="border-amber-200" />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-amber-900">Mailing List</p>
                    <p className="text-sm text-amber-600">Subscribe to our newsletter and updates</p>
                  </div>
                  <Switch checked={preferences.mailingList} onCheckedChange={checked => handlePreferenceChange("mailingList", checked)} />
                </div>
                <Separator className="border-amber-200" />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-amber-900">Product Updates</p>
                    <p className="text-sm text-amber-600">Get notified about new features and improvements</p>
                  </div>
                  <Switch checked={preferences.productUpdates} onCheckedChange={checked => handlePreferenceChange("productUpdates", checked)} />
                </div>
                <Separator className="border-amber-200" />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-amber-900">Marketing Emails</p>
                    <p className="text-sm text-amber-600">Receive promotional offers and tips</p>
                  </div>
                  <Switch checked={preferences.marketingEmails} onCheckedChange={checked => handlePreferenceChange("marketingEmails", checked)} />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security */}
          <Card className="border-amber-200 bg-white/70">
            <CardHeader>
              <CardTitle className="text-amber-900 flex items-center text-2xl">
                <Shield className="w-6 h-6 mr-2 text-amber-600" /> Security
              </CardTitle>
              <CardDescription className="text-amber-700">Manage your account security settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-amber-900">Two-Factor Authentication</p>
                  <p className="text-sm text-amber-600">Add an extra layer of security to your account</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch checked={preferences.twoFactorAuth} onCheckedChange={checked => handlePreferenceChange("twoFactorAuth", checked)} />
                  {!preferences.twoFactorAuth && (
                    <Button size="sm" variant="outline" className="border-amber-300 text-amber-800 hover:bg-amber-50 bg-transparent">Setup</Button>
                  )}
                </div>
              </div>
              <Separator className="border-amber-200" />
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-amber-900">Change Password</p>
                  <p className="text-sm text-amber-600">Update your account password</p>
                </div>
                <Button variant="outline" className="border-amber-300 text-amber-800 hover:bg-amber-50 bg-transparent">Change Password</Button>
              </div>
            </CardContent>
          </Card>

          {/* Data & Privacy */}
          <Card className="border-amber-200 bg-white/70">
            <CardHeader>
              <CardTitle className="text-amber-900 flex items-center text-2xl">
                <Download className="w-6 h-6 mr-2 text-amber-600" /> Data & Privacy
              </CardTitle>
              <CardDescription className="text-amber-700">Control your data and privacy settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-amber-900">Download Your Data</p>
                  <p className="text-sm text-amber-600">Export all your documents and citations</p>
                </div>
                <Button variant="outline" className="border-amber-300 text-amber-800 hover:bg-amber-50 bg-transparent">
                  <Download className="w-4 h-4 mr-2" /> Export Data
                </Button>
              </div>
              <Separator className="border-amber-200" />
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-red-600">Delete Account</p>
                  <p className="text-sm text-amber-600">Permanently delete your account and all data</p>
                </div>
                <Button variant="outline" className="border-red-300 text-red-600 hover:bg-red-50 bg-transparent">
                  <Trash2 className="w-4 h-4 mr-2" /> Delete Account
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 