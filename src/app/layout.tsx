import type { Metadata } from 'next'
import './globals.css'
import { AuthProvider } from '@/contexts/AuthContext'
import { PlanProvider } from '@/contexts/PlanContext'
import { AuthErrorBoundary } from '@/components/AuthErrorBoundary'

export const metadata: Metadata = {
  title: 'CiteAI - AI-Powered Citation Generator',
  description: 'Transform your essays into perfectly formatted works cited pages in seconds. CiteAI automatically detects sources and generates accurate citations in any format.',
  generator: 'Next.js',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <AuthErrorBoundary>
          <AuthProvider>
            <PlanProvider>
              {children}
            </PlanProvider>
          </AuthProvider>
        </AuthErrorBoundary>
      </body>
    </html>
  )
}
