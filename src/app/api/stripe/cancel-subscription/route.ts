import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { supabase } from '@/lib/supabase';


export async function POST(request: NextRequest) {
  try {
    console.log('Cancel subscription request received');
    const { subscriptionId } = await request.json();

    console.log('Subscription ID to cancel:', subscriptionId);

    if (!subscriptionId) {
      console.error('Missing subscription ID');
      return NextResponse.json(
        { error: 'Missing subscription ID' },
        { status: 400 }
      );
    }

    // Cancel the subscription immediately (for testing)
    console.log('Canceling subscription in Stripe...');
    const subscription = await stripe.subscriptions.cancel(subscriptionId);

    console.log('Stripe subscription updated:', subscription.status, 'cancel_at_period_end:', subscription.cancel_at_period_end);

    // Update the database to reflect the cancellation
    const { data: subscriptionData, error: fetchError } = await supabase
      .from('user_subscriptions')
      .select('user_id')
      .eq('stripe_subscription_id', subscriptionId)
      .single();

    if (fetchError) {
      console.error('Error fetching subscription data:', fetchError);
    } else {
      console.log('Found user for subscription:', subscriptionData.user_id);
      
      // Update user_subscriptions table
      const { error: updateError } = await supabase
        .from('user_subscriptions')
        .update({
          status: 'canceled',
          updated_at: new Date().toISOString()
        })
        .eq('stripe_subscription_id', subscriptionId);

      if (updateError) {
        console.error('Error updating user_subscriptions:', updateError);
      } else {
        console.log('Successfully updated user_subscriptions status to canceled');
      }

      // Update users table to set subscription_tier back to free
      const { error: userError } = await supabase
        .from('users')
        .update({
          subscription_tier: 'free',
          plan_type: 'free',
          updated_at: new Date().toISOString()
        })
        .eq('id', subscriptionData.user_id);

      if (userError) {
        console.error('Error updating users table:', userError);
      } else {
        console.log('Successfully updated user subscription_tier to free');
      }
    }

    return NextResponse.json({ 
      success: true, 
      subscription: {
        id: subscription.id,
        status: subscription.status,
        cancel_at_period_end: subscription.cancel_at_period_end,
      }
    });
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to cancel subscription' },
      { status: 500 }
    );
  }
} 