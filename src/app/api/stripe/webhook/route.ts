import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import Strip<PERSON> from 'stripe';
import { supabase } from '@/lib/supabase';


export async function POST(request: NextRequest) {
  const body = await request.text();
  const signature = request.headers.get('stripe-signature')!;

  // Add debug logging
  console.log('Webhook received - body length:', body.length);
  console.log('Stripe signature present:', !!signature);

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
    console.log('Webhook signature verified successfully');
    console.log('Event type:', event.type);
  } catch (error) {
    console.error('Webhook signature verification failed:', error);
    return NextResponse.json(
      { error: 'Invalid signature' },
      { status: 400 }
    );
  }

  try {
    console.log('Processing webhook event type:', event.type);
    
    switch (event.type) {
      case 'checkout.session.completed':
        console.log('Handling checkout.session.completed event');
        const session = event.data.object as Stripe.Checkout.Session;
        
        // Check if this is a microtransaction
        if (session.metadata?.type === 'microtransaction') {
          await handleMicrotransactionCompleted(session);
        } else {
          await handleCheckoutCompleted(session);
        }
        break;

      case 'customer.subscription.created':
        console.log('Handling customer.subscription.created event');
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionUpdate(subscription);
        break;

      case 'customer.subscription.updated':
        console.log('Handling customer.subscription.updated event');
        const updatedSubscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionUpdate(updatedSubscription);
        break;

      case 'customer.subscription.deleted':
        console.log('Handling customer.subscription.deleted event');
        const deletedSubscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionDeleted(deletedSubscription);
        break;

      case 'invoice.payment_succeeded':
        console.log('Handling invoice.payment_succeeded event');
        const invoice = event.data.object as Stripe.Invoice;
        await handlePaymentSucceeded(invoice);
        break;

      case 'invoice.payment_failed':
        console.log('Handling invoice.payment_failed event');
        const failedInvoice = event.data.object as Stripe.Invoice;
        await handlePaymentFailed(failedInvoice);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    console.log('Webhook processing completed successfully');
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  console.log('=== handleCheckoutCompleted START ===');
  const userId = session.metadata?.userId;
  const planType = session.metadata?.planType;

  console.log('Session metadata:', session.metadata);
  console.log('Extracted userId:', userId);
  console.log('Extracted planType:', planType);

  if (!userId || !planType) {
    console.error('Missing metadata in checkout session');
    console.error('Session metadata:', session.metadata);
    console.log('=== handleCheckoutCompleted END (missing metadata) ===');
    return;
  }

  console.log('Processing checkout completed for user:', userId, 'plan:', planType);
  console.log('Stripe customer ID:', session.customer);
  console.log('Stripe subscription ID:', session.subscription);
  console.log('Session metadata:', session.metadata);
  
  // Check if this customer ID already exists for a different user
  if (session.customer) {
    const { data: existingCustomer, error: customerCheckError } = await supabase
      .from('user_subscriptions')
      .select('user_id, stripe_customer_id')
      .eq('stripe_customer_id', session.customer)
      .single();
      
    if (existingCustomer && existingCustomer.user_id !== userId) {
      console.warn('Customer ID already exists for different user:', {
        existingUserId: existingCustomer.user_id,
        newUserId: userId,
        customerId: session.customer
      });
    }
  }

  try {
    // First, check if user exists in users table
    const { data: userData, error: userCheckError } = await supabase
      .from('users')
      .select('id, subscription_tier')
      .eq('id', userId)
      .single();

    if (userCheckError) {
      console.error('Error checking if user exists:', userCheckError);
      throw userCheckError;
    }

    console.log('Current user data:', userData);

    // Check if user already has a subscription record
    const { data: existingSubscription, error: checkError } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking existing subscription:', checkError);
      throw checkError;
    }

    if (existingSubscription) {
      // First, deactivate any existing active subscriptions
      console.log('Deactivating existing subscription for user:', userId);
      const { error: deactivateError } = await supabase
        .from('user_subscriptions')
        .update({ status: 'canceled' })
        .eq('user_id', userId)
        .eq('status', 'active');

      if (deactivateError) {
        console.error('Error deactivating existing subscription:', deactivateError);
        throw deactivateError;
      }

      // Then update the existing record with new Stripe data
      console.log('Updating existing subscription for user:', userId);
      const { error: updateError } = await supabase
        .from('user_subscriptions')
        .update({
          stripe_customer_id: session.customer as string,
          stripe_subscription_id: session.subscription as string,
          plan_type: planType,
          status: 'active',
          current_period_start: new Date().toISOString(),
          current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        })
        .eq('user_id', userId);

      if (updateError) {
        console.error('Error updating existing subscription:', updateError);
        throw updateError;
      }
    } else {
      // Insert new subscription
      console.log('Creating new subscription for user:', userId);
      const { error: insertError } = await supabase
        .from('user_subscriptions')
        .insert({
          user_id: userId,
          stripe_customer_id: session.customer as string,
          stripe_subscription_id: session.subscription as string,
          plan_type: planType,
          status: 'active',
          current_period_start: new Date().toISOString(),
          current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        });

      if (insertError) {
        console.error('Error inserting new subscription:', insertError);
        throw insertError;
      }
    }

    console.log('Successfully updated user_subscriptions table');

    // CRITICAL: Update the users table with the subscription tier
    console.log('About to update users table subscription_tier to:', planType);
    console.log('User ID being updated:', userId);
    
    const { error: userError } = await supabase
      .from('users')
      .update({ 
        subscription_tier: planType,
        plan_type: planType, // Also update plan_type field
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (userError) {
      console.error('Error updating users table:', userError);
      console.error('Error details:', {
        code: userError.code,
        message: userError.message,
        details: userError.details,
        hint: userError.hint
      });
      throw userError;
    }

    console.log('Successfully updated users table subscription_tier to:', planType);

    // Verify the update worked
    console.log('Verifying the update...');
    const { data: updatedUserData, error: verifyError } = await supabase
      .from('users')
      .select('id, subscription_tier, plan_type')
      .eq('id', userId)
      .single();

    if (verifyError) {
      console.error('Error verifying user update:', verifyError);
    } else {
      console.log('Verified user update - new subscription_tier:', updatedUserData?.subscription_tier);
      console.log('Verified user update - new plan_type:', updatedUserData?.plan_type);
    }

    console.log('=== handleCheckoutCompleted END (success) ===');

  } catch (error) {
    console.error('Error in handleCheckoutCompleted:', error);
    console.error('Error details:', {
      userId,
      planType,
      customerId: session.customer,
      subscriptionId: session.subscription,
      error: error instanceof Error ? error.message : error
    });
    throw error;
  }
}

async function handleSubscriptionUpdate(subscription: Stripe.Subscription) {
  console.log('Processing subscription update:', subscription.id, 'status:', subscription.status);
  
  try {
    // First, get the user_id from user_subscriptions table
    const { data: subscriptionData, error: fetchError } = await supabase
      .from('user_subscriptions')
      .select('user_id, plan_type')
      .eq('stripe_subscription_id', subscription.id)
      .single();

    if (fetchError) {
      console.error('Error fetching subscription data:', fetchError);
      throw fetchError;
    }

    // Update user_subscriptions table
    const { error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .update({
        status: subscription.status,
      })
      .eq('stripe_subscription_id', subscription.id);

    if (subscriptionError) {
      console.error('Error updating user_subscriptions:', subscriptionError);
      throw subscriptionError;
    }

    // Update users table subscription_tier based on subscription status
    let newTier = subscriptionData.plan_type;
    if (subscription.status === 'canceled' || subscription.status === 'unpaid') {
      newTier = 'free';
    }

    const { error: userError } = await supabase
      .from('users')
      .update({
        subscription_tier: newTier,
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriptionData.user_id);

    if (userError) {
      console.error('Error updating users table:', userError);
      throw userError;
    }

    console.log('Successfully updated both tables for subscription:', subscription.id);
    console.log('Updated user subscription_tier to:', newTier);
  } catch (error) {
    console.error('Error in handleSubscriptionUpdate:', error);
    throw error;
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  try {
    // First, get the user_id from user_subscriptions table
    const { data: subscriptionData, error: fetchError } = await supabase
      .from('user_subscriptions')
      .select('user_id')
      .eq('stripe_subscription_id', subscription.id)
      .single();

    if (fetchError) {
      console.error('Error fetching subscription data for deletion:', fetchError);
      throw fetchError;
    }

    // Update user_subscriptions table
    const { error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'canceled',
      })
      .eq('stripe_subscription_id', subscription.id);

    if (subscriptionError) {
      console.error('Error updating user_subscriptions for deletion:', subscriptionError);
      throw subscriptionError;
    }

    // Update users table to set subscription_tier back to 'free'
    const { error: userError } = await supabase
      .from('users')
      .update({
        subscription_tier: 'free',
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriptionData.user_id);

    if (userError) {
      console.error('Error updating users table for deletion:', userError);
      throw userError;
    }

    console.log('Successfully canceled subscription and reset user to free tier:', subscription.id);
  } catch (error) {
    console.error('Error in handleSubscriptionDeleted:', error);
    throw error;
  }
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  // Handle successful payment
  console.log('Payment succeeded for invoice:', invoice.id);
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  // Handle failed payment
  console.log('Payment failed for invoice:', invoice.id);
}

async function handleMicrotransactionCompleted(session: Stripe.Checkout.Session) {
  console.log('=== handleMicrotransactionCompleted START ===');
  
  const userId = session.metadata?.userId;
  const productId = session.metadata?.productId;
  const citations = parseInt(session.metadata?.citations || '0');
  const documentId = session.metadata?.documentId;

  console.log('Microtransaction session metadata:', session.metadata);
  console.log('Extracted userId:', userId);
  console.log('Extracted productId:', productId);
  console.log('Extracted citations:', citations);

  if (!userId || !productId || !citations) {
    console.error('Missing metadata in microtransaction session');
    console.log('=== handleMicrotransactionCompleted END (missing metadata) ===');
    return;
  }

  try {
    // Add purchased citations to user's account
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('purchased_citations')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error fetching user data:', userError);
      throw userError;
    }

    const currentPurchasedCitations = userData?.purchased_citations || 0;
    const newPurchasedCitations = currentPurchasedCitations + citations;

    const { error: updateError } = await supabase
      .from('users')
      .update({
        purchased_citations: newPurchasedCitations,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      console.error('Error updating user citations:', updateError);
      throw updateError;
    }

    // Log the microtransaction for audit purposes (optional)
    console.log(`Successfully added ${citations} citations to user ${userId}`);
    console.log(`Product ID: ${productId}, Session ID: ${session.id}`);
    console.log('=== handleMicrotransactionCompleted END (success) ===');

  } catch (error) {
    console.error('Error in handleMicrotransactionCompleted:', error);
    console.error('Error details:', {
      userId,
      productId,
      citations,
      documentId,
      error: error instanceof Error ? error.message : error
    });
    throw error;
  }
} 