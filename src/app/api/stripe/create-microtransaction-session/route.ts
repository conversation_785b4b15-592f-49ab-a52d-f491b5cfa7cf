import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { supabase } from '@/lib/supabase';
import { MICROTRANSACTION_PRODUCTS } from '@/lib/stripe-client';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Microtransaction request body:', body);
    
    const { productId, userId, userEmail, documentId } = body;

    if (!userId || !productId || !userEmail) {
      console.log('Missing parameters:', { userId, productId, userEmail });
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Get the microtransaction product details
    console.log('Looking for product ID:', productId);
    console.log('Available microtransaction products:', MICROTRANSACTION_PRODUCTS);
    
    const product = Object.values(MICROTRANSACTION_PRODUCTS).find(p => p.id === productId);
    if (!product) {
      console.log('Product not found for product ID:', productId);
      return NextResponse.json(
        { error: 'Invalid product ID' },
        { status: 400 }
      );
    }
    
    console.log('Found microtransaction product:', product);

    // Create Stripe checkout session for one-time payment
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: product.name,
              description: product.description,
            },
            unit_amount: Math.round(product.price * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment', // One-time payment, not subscription
      success_url: `${request.headers.get('origin')}${documentId ? `/document/${documentId}` : '/dashboard'}?success=true&session_id={CHECKOUT_SESSION_ID}&product_id=${productId}&source=${documentId ? 'document' : 'dashboard'}`,
      cancel_url: `${request.headers.get('origin')}${documentId ? `/document/${documentId}` : '/dashboard'}?canceled=true&source=${documentId ? 'document' : 'dashboard'}`,
      metadata: {
        userId,
        productId,
        citations: product.citations.toString(),
        documentId: documentId || '',
        type: 'microtransaction'
      },
      customer_email: userEmail,
    });

    return NextResponse.json({ sessionId: session.id });
  } catch (error) {
    console.error('Error creating microtransaction checkout session:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
