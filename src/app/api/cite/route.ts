import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { citationRateLimiter, getClientIdentifier } from '@/lib/rate-limiter';

// 动态导入amplify_outputs.json
const getAmplifyOutputs = async () => {
  try {
    const outputs = await import('../../../../amplify_outputs.json');
    return outputs.default || outputs;
  } catch (error) {
    console.error('Failed to load amplify outputs:', error);
    throw error;
  }
};

// 初始化Supabase admin客户端
const supabaseAdmin = supabase;

// 定义Lambda响应接口
interface LambdaResponse {
  output_type: 'html' | 'text' | 'pdf';
  data: any;
  citations?: any[];
  citation_count?: number;
  error?: string;
}

export async function POST(req: NextRequest) {
  console.log('CITEAI GENERATE API ROUTE HIT');
  
  try {
    // 1. 解析请求参数
    const requestBody = await req.json();
    const { title, content, citation_format, plan, html, pdf, userId } = requestBody;
    
    console.log('Received request:', {
      title: title?.substring(0, 50) + '...',
      contentLength: content?.length,
      citation_format,
      plan,
      html,
      pdf,
      userId
    });

    // 2. 验证必需字段
    if (!title || !content || !citation_format || !plan || !userId) {
      console.error('Missing required fields:', {
        hasTitle: !!title,
        hasContent: !!content,
        hasCitationFormat: !!citation_format,
        hasPlan: !!plan,
        hasUserId: !!userId
      });
      return NextResponse.json({
        error: 'Missing required fields: title, content, citation_format, plan, userId'
      }, { status: 400 });
    }

    // Apply rate limiting for citation generation
    const clientId = getClientIdentifier(req, userId);
    const rateLimit = citationRateLimiter.checkLimit(clientId);

    if (!rateLimit.allowed) {
      return NextResponse.json({
        error: 'Too many citation requests. Please try again later.',
        resetTime: rateLimit.resetTime
      }, {
        status: 429,
        headers: {
          'X-RateLimit-Limit': '50',
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': rateLimit.resetTime.toString()
        }
      });
    }

    // 3. 检查用户引用限制（Pro用户除外）
    if (plan !== 'pro') {
      console.log('Checking citation limit for user:', userId);
      
      const { data: availableCitations, error: limitError } = await supabaseAdmin.rpc('get_available_citations', {
        user_id: userId
      });

      console.log('Citation limit check result:', { availableCitations, limitError });

      if (limitError) {
        console.error('Error checking citation limit:', limitError);
        return NextResponse.json({ 
          error: 'Failed to check citation limit',
          details: limitError.message 
        }, { status: 500 });
      }

      if (!availableCitations || availableCitations <= 0) {
        console.log('Citation limit reached for user:', userId);
        return NextResponse.json({ 
          error: 'Citation limit reached',
          limitReached: true,
          availableCitations: availableCitations || 0
        }, { status: 403 });
      }
      
      console.log('Citation limit check passed, available:', availableCitations);
    }

    // 4. 获取Lambda函数URL
    const outputs = await getAmplifyOutputs();
    console.log('Amplify outputs loaded:', {
      hasCustom: !!outputs.custom,
      customKeys: outputs.custom ? Object.keys(outputs.custom) : []
    });
    
    const citationFunctionUrl = outputs.custom?.citationFunctionUrl;
    
    if (!citationFunctionUrl) {
      console.error('Citation function URL not found in amplify_outputs.json');
      console.error('Available custom fields:', outputs.custom);
      return NextResponse.json({ 
        error: 'Citation service not available - function URL not configured' 
      }, { status: 500 });
    }

    console.log('Using citation function URL:', citationFunctionUrl.substring(0, 50) + '...');

    // 5. 准备Lambda请求payload
    const output_mode = pdf ? 'pdf' : (html ? 'html' : 'text');
    
    const lambdaPayload = {
      title,
      content,
      citation_format: citation_format.toLowerCase(),
      plan: plan.toLowerCase(),
      output_mode,
      format_only: false,
      csl_items: []
    };
    
    console.log('Sending payload to Lambda:', {
      ...lambdaPayload,
      content: `[${lambdaPayload.content.length} characters]`
    });

    // 6. 调用Lambda函数
    const lambdaResponse = await fetch(citationFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(lambdaPayload),
    });

    console.log('Lambda response status:', lambdaResponse.status);
    console.log('Lambda response headers:', Object.fromEntries(lambdaResponse.headers.entries()));

    if (!lambdaResponse.ok) {
      const errorText = await lambdaResponse.text();
      console.error(`Lambda function returned ${lambdaResponse.status}:`, errorText);

      // 为不同的错误状态码提供用户友好的消息
      let userMessage = 'Citation service is temporarily unavailable';
      if (lambdaResponse.status === 502) {
        userMessage = 'Citation service is experiencing technical difficulties. Please refresh the page or contact IT support if the problem persists.';
      } else if (lambdaResponse.status === 500) {
        userMessage = 'Internal citation service error. Please try again or contact IT support.';
      } else if (lambdaResponse.status === 503) {
        userMessage = 'Citation service is temporarily overloaded. Please try again in a few moments.';
      }

      return NextResponse.json({
        error: userMessage,
        details: `Service error (${lambdaResponse.status})`,
        technicalDetails: errorText
      }, { status: lambdaResponse.status });
    }

    // 7. 解析Lambda响应
    let responseJson: LambdaResponse;
    const responseText = await lambdaResponse.text();
    
    try {
      responseJson = JSON.parse(responseText) as LambdaResponse;
      console.log('Lambda response parsed successfully:', {
        output_type: responseJson.output_type,
        hasData: !!responseJson.data,
        citation_count: responseJson.citation_count,
        hasError: !!responseJson.error
      });
    } catch (parseError) {
      console.error('Failed to parse Lambda response as JSON:', parseError);
      console.error('Raw response:', responseText);
      
      return NextResponse.json({ 
        error: 'Invalid response from citation service',
        details: 'Response is not valid JSON'
      }, { status: 500 });
    }

    // 8. 检查Lambda返回的错误
    if (responseJson.error) {
      console.error('Lambda returned error:', responseJson.error);

      // 检查是否是依赖导入错误
      const isImportError = responseJson.error.includes('import failed') ||
                           responseJson.error.includes('No module named') ||
                           responseJson.error.includes('Critical dependency');

      const userMessage = isImportError
        ? 'Citation service is experiencing configuration issues. Please refresh the page or contact IT support.'
        : 'Citation generation failed due to a service error. Please try again or contact IT support if the problem persists.';

      return NextResponse.json({
        error: userMessage,
        details: 'Service configuration error',
        technicalDetails: responseJson.error
      }, { status: 500 });
    }

    // 9. 验证响应结构
    if (!responseJson.output_type || !responseJson.data) {
      console.error('Invalid response structure from Lambda:', responseJson);
      return NextResponse.json({ 
        error: 'Invalid response from citation service',
        details: 'Missing output_type or data'
      }, { status: 500 });
    }

    // 10. 消费引用配额（基于实际生成的引用数量）
    const actualCitationCount = responseJson.citation_count || 0;
    
    if (plan !== 'pro' && actualCitationCount > 0) {
      console.log('Consuming citations:', actualCitationCount, 'for user:', userId);
      
      const { data: consumeSuccess, error: consumeError } = await supabaseAdmin.rpc(
        'consume_citation', 
        {
          count_to_consume: actualCitationCount,
          user_id: userId,
        }
      );

      if (consumeError) {
        console.error('Error consuming citations:', consumeError);
        // 不让这个错误阻断请求，因为用户已经得到了引用
      } else {
        console.log('Successfully consumed', actualCitationCount, 'citations');
      }
    }

    // 11. 根据输出类型返回相应格式的响应
    console.log('About to process response data (cite):', {
      output_type: responseJson.output_type,
      dataType: typeof responseJson.data,
      dataLength: typeof responseJson.data === 'string' ? responseJson.data.length : 'not string',
      dataPreview: typeof responseJson.data === 'string' ? responseJson.data.substring(0, 100) + '...' : responseJson.data,
      citation_count: actualCitationCount
    });

    switch (responseJson.output_type) {
      case 'html':
        console.log(
          'Returning HTML response (cite):', 
          {
            citation_html: responseJson.data.substring(0, 100) + '...',
            citation_count: actualCitationCount,
            citations: responseJson.citations || []
          }
        );
        return NextResponse.json({ 
          citation_html: responseJson.data,
          citation_count: actualCitationCount,
          citations: responseJson.citations || []
        });
        
      case 'text':
        return NextResponse.json({ 
          citation: responseJson.data,
          citation_count: actualCitationCount,
          citations: responseJson.citations || []
        });
        
      case 'pdf':
        // PDF情况下，Lambda返回S3的预签名URL
        if (responseJson.data && responseJson.data.download_url) {
          return NextResponse.json({ 
            pdf_download_url: responseJson.data.download_url,
            citation_count: actualCitationCount,
            citations: responseJson.citations || []
          });
        } else {
          console.error('PDF response missing download_url:', responseJson.data);
          return NextResponse.json({ 
            error: 'PDF generation failed',
            details: 'No download URL provided'
          }, { status: 500 });
        }
        
      default:
        console.error('Unknown output type from Lambda:', responseJson.output_type);
        return NextResponse.json({ 
          error: 'Unknown response format from citation service',
          details: `Unexpected output_type: ${responseJson.output_type}`
        }, { status: 500 });
    }

  } catch (err: any) {
    console.error('Unexpected error in generate API route:', err);
    console.error('Error stack:', err.stack);
    
    return NextResponse.json({ 
      error: 'Internal server error',
      details: err.message 
    }, { status: 500 });
  }
}