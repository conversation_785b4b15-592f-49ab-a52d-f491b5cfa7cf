import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(req: NextRequest) {
  try {
    // Get user ID from the request headers or session
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    // Extract the token from the Authorization header
    const token = authHeader.replace('Bearer ', '');
    
    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Get document counts using the database function
    const { data, error } = await supabase.rpc('get_document_counts', {
      user_uuid: user.id
    });

    if (error) {
      console.error('Error getting document counts:', error);
      return NextResponse.json({ error: 'Failed to get document counts' }, { status: 500 });
    }

    return NextResponse.json({
      active: data[0]?.active_count || 0,
      deleted: data[0]?.deleted_count || 0
    });

  } catch (error) {
    console.error('Error in document counts API:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
