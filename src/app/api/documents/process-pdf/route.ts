/**
 * PDF文档处理API端点
 * 仅供Pro用户使用，用于上传PDF文件并提取文本内容
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { pdfRateLimiter, getClientIdentifier } from '@/lib/rate-limiter';
import { createClient } from '@supabase/supabase-js';

// 获取Amplify输出配置
const getAmplifyOutputs = async () => {
  try {
    const outputs = await import('../../../../../amplify_outputs.json');
    return outputs.default || outputs;
  } catch (error) {
    console.error('Failed to load amplify outputs:', error);
    throw error;
  }
};

// 定义PDF处理器API响应接口
interface PdfProcessorResponse {
  document_id: string;
  filename: string;
  extracted_text: string;
  page_count: number;
  word_count: number;
  extraction_method: string;
  success: boolean;
  s3_key?: string;
  file_size_mb: number;
  extraction_error?: string;
}

export async function POST(req: NextRequest) {
  try {
    // 验证用户身份和权限
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // 创建Supabase客户端用于验证用户
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader,
          },
        },
      }
    );

    // 获取当前用户
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid authentication' }, { status: 401 });
    }

    // Apply rate limiting for PDF processing
    const clientId = getClientIdentifier(req, user.id);
    const rateLimit = pdfRateLimiter.checkLimit(clientId);

    if (!rateLimit.allowed) {
      return NextResponse.json({
        error: 'Too many PDF processing requests. Please try again later.',
        resetTime: rateLimit.resetTime
      }, {
        status: 429,
        headers: {
          'X-RateLimit-Limit': '10',
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': rateLimit.resetTime.toString()
        }
      });
    }

    // 检查用户订阅计划 - 只有Pro用户可以使用PDF处理功能
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('subscription_plan')
      .eq('id', user.id)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (profile.subscription_plan !== 'pro') {
      return NextResponse.json({
        error: 'PDF processing is only available for Pro users. Please upgrade your plan.'
      }, { status: 403 });
    }

    const formData = await req.formData();
    const pdfFile = formData.get('pdf') as File;
    const documentId = formData.get('documentId') as string;

    if (!pdfFile || !documentId) {
      return NextResponse.json({ error: 'PDF file and document ID are required' }, { status: 400 });
    }

    // 验证文档所有权
    const { data: document, error: docError } = await supabase
      .from('documents')
      .select('user_id')
      .eq('id', documentId)
      .single();

    if (docError || !document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    if (document.user_id !== user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // 检查文件是否为PDF - 使用多重验证
    const allowedMimeTypes = ['application/pdf'];
    const allowedExtensions = ['.pdf'];

    if (!allowedMimeTypes.includes(pdfFile.type)) {
      return NextResponse.json({ error: 'File must be a PDF' }, { status: 400 });
    }

    // 验证文件扩展名
    const fileExtension = pdfFile.name.toLowerCase().slice(pdfFile.name.lastIndexOf('.'));
    if (!allowedExtensions.includes(fileExtension)) {
      return NextResponse.json({ error: 'File must have a .pdf extension' }, { status: 400 });
    }

    // 检查文件大小（限制为10MB）
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (pdfFile.size > maxSize) {
      return NextResponse.json({
        error: `PDF file too large. Maximum size is ${maxSize / (1024 * 1024)}MB`
      }, { status: 400 });
    }

    // 检查最小文件大小（防止空文件）
    const minSize = 100; // 100 bytes
    if (pdfFile.size < minSize) {
      return NextResponse.json({
        error: 'PDF file appears to be empty or corrupted'
      }, { status: 400 });
    }

    // 将PDF文件转换为Base64
    const arrayBuffer = await pdfFile.arrayBuffer();
    const pdfBase64 = Buffer.from(arrayBuffer).toString('base64');

    // 获取PDF处理器Lambda函数URL
    const outputs = await getAmplifyOutputs();
    const pdfProcessorUrl = (outputs.custom as any)?.pdfProcessorUrl;

    if (!pdfProcessorUrl) {
      console.error('PDF processor URL not found in amplify_outputs.json');
      return NextResponse.json({
        error: 'PDF processing service not available'
      }, { status: 500 });
    }

    // 调用PDF处理器Lambda函数
    const lambdaPayload = {
      pdf_data: pdfBase64,
      filename: pdfFile.name,
      document_id: documentId
    };

    console.log(`Calling PDF processor for document ${documentId}, file: ${pdfFile.name}`);

    const response = await fetch(pdfProcessorUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(lambdaPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`PDF processor returned ${response.status}: ${errorText}`);
      return NextResponse.json({
        error: 'Failed to process PDF',
        details: `Lambda returned ${response.status}: ${errorText}`
      }, { status: response.status });
    }

    const processingResult = await response.json() as PdfProcessorResponse;
    console.log('PDF processing result:', processingResult);

    // 更新Supabase中的文档内容
    const { error: updateError } = await supabase
      .from('documents')
      .update({
        content: processingResult.extracted_text,
        word_count: processingResult.word_count
      })
      .eq('id', documentId);

    if (updateError) {
      console.error('Error updating document in Supabase:', updateError);
      return NextResponse.json({
        error: 'Failed to save extracted text to database'
      }, { status: 500 });
    }

    // 返回处理结果
    return NextResponse.json({
      extractedText: processingResult.extracted_text,
      wordCount: processingResult.word_count,
      pages: processingResult.page_count,
      fileSizeMB: processingResult.file_size_mb,
      extractionMethod: processingResult.extraction_method,
      success: processingResult.success,
      message: `PDF processed successfully: ${pdfFile.name}`,
      ...(processingResult.extraction_error && {
        extractionError: processingResult.extraction_error
      })
    });

  } catch (error) {
    // Log detailed error for debugging but don't expose sensitive information
    console.error('Error in PDF processing API:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });

    // Return generic error message to prevent information leakage
    return NextResponse.json({
      error: 'An error occurred while processing the PDF. Please try again later.'
    }, { status: 500 });
  }
}
