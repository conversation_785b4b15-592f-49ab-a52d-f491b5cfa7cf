/**
 * 演示版PDF文档处理API端点
 * 仅用于测试PDF处理功能，不保存到数据库
 */

import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

const execAsync = promisify(exec);

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const pdfFile = formData.get('pdf') as File;
    const documentId = formData.get('documentId') as string;

    if (!pdfFile || !documentId) {
      return NextResponse.json({ error: 'PDF file and document ID are required' }, { status: 400 });
    }

    // 检查文件是否为PDF
    if (pdfFile.type !== 'application/pdf') {
      return NextResponse.json({ error: 'File must be a PDF' }, { status: 400 });
    }

    // 检查文件大小（限制为10MB）
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (pdfFile.size > maxSize) {
      return NextResponse.json({ 
        error: `PDF file too large. Maximum size is ${maxSize / (1024 * 1024)}MB` 
      }, { status: 400 });
    }

    // 创建临时文件
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempFileName = `${uuidv4()}.pdf`;
    const tempFilePath = path.join(tempDir, tempFileName);

    // 保存上传的PDF文件
    const arrayBuffer = await pdfFile.arrayBuffer();
    fs.writeFileSync(tempFilePath, Buffer.from(arrayBuffer));

    let extractedText = '';
    let pageCount = 1;
    let extractionMethod = 'demo-fallback';
    let success = false;
    let extractionError = '';

    try {
      // 使用独立的Python脚本提取文本
      const scriptPath = path.join(process.cwd(), 'test', 'extract_pdf_text.py');

      try {
        const { stdout, stderr } = await execAsync(`python3 "${scriptPath}" "${tempFilePath}"`);

        if (stderr) {
          console.log('Python stderr:', stderr);
        }

        const result = JSON.parse(stdout.trim());

        extractedText = result.text;
        pageCount = result.page_count;
        extractionMethod = result.extraction_method;
        success = result.success;

        if (result.error) {
          extractionError = result.error;
        }

      } catch (pythonError) {
        console.log('Python extraction failed, using fallback method:', pythonError);
        
        // 备用方法：返回占位符文本
        extractedText = `PDF文档已成功上传：${pdfFile.name}

由于本地环境限制，当前无法自动提取PDF文本内容。

请手动复制粘贴PDF中的文本内容，或者：
1. 使用在线PDF转文本工具
2. 在本地使用PDF阅读器复制文本
3. 安装PyPDF2库：pip install PyPDF2

文件信息：
- 文件名：${pdfFile.name}
- 文件大小：${(pdfFile.size / 1024 / 1024).toFixed(2)} MB
- 处理时间：${new Date().toLocaleString()}

错误详情：${pythonError}

这是演示版本，生产环境将使用AWS Lambda进行处理。`;
        
        pageCount = 1;
        extractionMethod = 'demo-fallback';
        success = false;
        extractionError = String(pythonError);
      }

    } finally {
      // 清理临时文件
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    }

    // 计算字数
    const wordCount = extractedText.trim().split(/\s+/).filter(word => word.length > 0).length;

    // 返回处理结果（不保存到数据库）
    return NextResponse.json({
      extractedText,
      wordCount,
      pages: pageCount,
      fileSizeMB: Number((pdfFile.size / 1024 / 1024).toFixed(2)),
      extractionMethod,
      success,
      message: `PDF processed in demo mode: ${pdfFile.name}`,
      note: 'Demo version - not saved to database. Production uses AWS Lambda.',
      ...(extractionError && { extractionError })
    });

  } catch (error) {
    console.error('Error in demo PDF processing API:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
