/**
 * 本地PDF文档处理API端点（用于测试）
 * 使用本地Python脚本处理PDF文件，不依赖Lambda函数
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

const execAsync = promisify(exec);

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const pdfFile = formData.get('pdf') as File;
    const documentId = formData.get('documentId') as string;

    if (!pdfFile || !documentId) {
      return NextResponse.json({ error: 'PDF file and document ID are required' }, { status: 400 });
    }

    // 检查文件是否为PDF
    if (pdfFile.type !== 'application/pdf') {
      return NextResponse.json({ error: 'File must be a PDF' }, { status: 400 });
    }

    // 检查文件大小（限制为10MB）
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (pdfFile.size > maxSize) {
      return NextResponse.json({ 
        error: `PDF file too large. Maximum size is ${maxSize / (1024 * 1024)}MB` 
      }, { status: 400 });
    }

    // 创建临时文件
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempFileName = `${uuidv4()}.pdf`;
    const tempFilePath = path.join(tempDir, tempFileName);

    // 保存上传的PDF文件
    const arrayBuffer = await pdfFile.arrayBuffer();
    fs.writeFileSync(tempFilePath, Buffer.from(arrayBuffer));

    let extractedText = '';
    let pageCount = 1;
    let extractionMethod = 'local-fallback';
    let success = false;

    try {
      // 尝试使用Python PyPDF2提取文本
      const pythonScript = `
import sys
import PyPDF2
from io import BytesIO
import json

try:
    with open('${tempFilePath}', 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        
        text_content = []
        page_count = len(pdf_reader.pages)
        
        for page_num, page in enumerate(pdf_reader.pages, 1):
            try:
                page_text = page.extract_text()
                if page_text.strip():
                    text_content.append(f"--- Page {page_num} ---\\n{page_text}")
            except Exception as e:
                text_content.append(f"--- Page {page_num} ---\\n[Error extracting text from this page]")
        
        extracted_text = "\\n\\n".join(text_content)
        
        if not extracted_text.strip():
            extracted_text = """PDF文档已上传，但无法提取文本内容。

可能的原因：
1. PDF是扫描版本（图片格式），需要OCR处理
2. PDF使用了特殊编码或加密
3. PDF内容主要是图像或图表

建议：
- 如果是扫描版PDF，请使用OCR工具转换为文本
- 或者手动复制粘贴PDF中的文本内容
- 确保PDF文件没有密码保护"""
        
        result = {
            "text": extracted_text,
            "page_count": page_count,
            "word_count": len(extracted_text.split()),
            "extraction_method": "PyPDF2-local",
            "success": True
        }
        
        print(json.dumps(result))
        
except Exception as e:
    result = {
        "text": f"""PDF处理过程中发生错误：{str(e)}

请尝试：
1. 确保PDF文件没有损坏
2. 检查PDF文件大小（建议小于10MB）
3. 如果问题持续，请手动复制粘贴文本内容""",
        "page_count": 1,
        "word_count": 30,
        "extraction_method": "error",
        "success": False,
        "error": str(e)
    }
    print(json.dumps(result))
`;

      try {
        const { stdout } = await execAsync(`python3 -c "${pythonScript}"`);
        const result = JSON.parse(stdout.trim());
        
        extractedText = result.text;
        pageCount = result.page_count;
        extractionMethod = result.extraction_method;
        success = result.success;
        
      } catch (pythonError) {
        console.log('Python extraction failed, using fallback method:', pythonError);
        
        // 备用方法：返回占位符文本
        extractedText = `PDF文档已成功上传：${pdfFile.name}

由于本地环境限制，当前无法自动提取PDF文本内容。

请手动复制粘贴PDF中的文本内容，或者：
1. 使用在线PDF转文本工具
2. 在本地使用PDF阅读器复制文本
3. 安装PyPDF2库：pip install PyPDF2

文件信息：
- 文件名：${pdfFile.name}
- 文件大小：${(pdfFile.size / 1024 / 1024).toFixed(2)} MB
- 处理时间：${new Date().toLocaleString()}

我们正在努力改进PDF处理功能。`;
        
        pageCount = 1;
        extractionMethod = 'fallback';
        success = false;
      }

    } finally {
      // 清理临时文件
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    }

    // 计算字数
    const wordCount = extractedText.trim().split(/\s+/).filter(word => word.length > 0).length;

    // 更新Supabase中的文档内容
    const { error: updateError } = await supabase
      .from('documents')
      .update({ 
        content: extractedText,
        word_count: wordCount
      })
      .eq('id', documentId);

    if (updateError) {
      console.error('Error updating document in Supabase:', updateError);
      return NextResponse.json({ 
        error: 'Failed to save extracted text to database' 
      }, { status: 500 });
    }

    // 返回处理结果
    return NextResponse.json({
      extractedText,
      wordCount,
      pages: pageCount,
      fileSizeMB: Number((pdfFile.size / 1024 / 1024).toFixed(2)),
      extractionMethod,
      success,
      message: `PDF processed locally: ${pdfFile.name}`,
      note: 'This is a local test version. Production uses AWS Lambda.'
    });

  } catch (error) {
    console.error('Error in local PDF processing API:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
