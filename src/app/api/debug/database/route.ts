import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';


export async function GET(request: NextRequest) {
  try {
    // Check if user_subscriptions table exists
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .select('*')
      .limit(1);

    // Check users table structure
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    return NextResponse.json({
      user_subscriptions_table: {
        exists: !subscriptionError,
        error: subscriptionError?.message,
        sample_data: subscriptionData
      },
      users_table: {
        exists: !userError,
        error: userError?.message,
        columns: userData && userData.length > 0 ? Object.keys(userData[0]) : [],
        sample_data: userData
      }
    });
  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 