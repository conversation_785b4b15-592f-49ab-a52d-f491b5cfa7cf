import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';


export async function GET(request: NextRequest) {
  try {
    // Get all subscriptions
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('user_subscriptions')
      .select('*');

    if (subscriptionsError) {
      console.error('Error fetching subscriptions:', subscriptionsError);
      return NextResponse.json({ error: subscriptionsError.message }, { status: 500 });
    }

    // Get all users with their subscription tiers
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, subscription_tier, updated_at');

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return NextResponse.json({ error: usersError.message }, { status: 500 });
    }

    return NextResponse.json({
      subscriptions,
      users,
      subscription_count: subscriptions?.length || 0,
      user_count: users?.length || 0
    });
  } catch (error) {
    console.error('Debug subscriptions endpoint error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 