/**
 * Test API endpoint for PDF export functionality
 * This endpoint bypasses database queries and uses sample data for testing
 */

import { NextRequest, NextResponse } from 'next/server';

// Define an interface for the expected API response for type safety.
interface PdfApiResponse {
  download_url: string;
}

// Sample CSL data for testing
const sampleCslItems = [
  {
    "id": "test1",
    "type": "article-journal",
    "title": "The Impact of AI on Academic Writing",
    "author": [
      {"family": "Smith", "given": "<PERSON>"},
      {"family": "Doe", "given": "<PERSON>"}
    ],
    "container-title": "Journal of Educational Technology",
    "issued": {"date-parts": [[2024, 3, 15]]},
    "URL": "https://example.com/article1",
    "volume": "12",
    "issue": "3",
    "page": "45-67"
  },
  {
    "id": "test2", 
    "type": "webpage",
    "title": "How to Write Better Citations",
    "author": [
      {"family": "<PERSON>", "given": "<PERSON>"}
    ],
    "container-title": "Academic Writing Hub",
    "issued": {"date-parts": [[2024, 1, 20]]},
    "URL": "https://academicwriting.com/citations"
  }
];

// Function to get amplify outputs
const getAmplifyOutputs = async () => {
  try {
    const outputs = await import('../../../../../amplify_outputs.json');
    return outputs.default || outputs;
  } catch (error) {
    console.error('Failed to load amplify outputs:', error);
    throw error;
  }
};

export async function POST(req: NextRequest) {
  try {
    const { citationFormat = 'mla' } = await req.json();
    
    console.log(`Testing PDF export with format: ${citationFormat}`);

    // Prepare the payload for our dedicated PDF generator Lambda.
    const apiPayload = {
      csl_items: sampleCslItems,
      citation_format: citationFormat,
    };

    // Get the PDF generator function URL from outputs
    const outputs = await getAmplifyOutputs();
    const pdfGeneratorUrl = (outputs.custom as any)?.pdfGeneratorUrl;
    
    if (!pdfGeneratorUrl) {
      console.error('PDF generator URL not found in amplify_outputs.json');
      console.log('Available outputs:', JSON.stringify(outputs, null, 2));
      return NextResponse.json({ 
        error: 'PDF generator service not available',
        debug: 'PDF generator URL not found in amplify_outputs.json'
      }, { status: 500 });
    }

    console.log(`Calling PDF generator at: ${pdfGeneratorUrl}`);

    // Send the request directly to the Lambda function URL
    const response = await fetch(pdfGeneratorUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiPayload),
    });

    console.log(`PDF generator response status: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`PDF generator API returned ${response.status}: ${errorText}`);
      return NextResponse.json({ 
        error: 'Failed to generate PDF',
        debug: `Lambda returned ${response.status}: ${errorText}`
      }, { status: response.status });
    }

    const responseJson = await response.json() as PdfApiResponse;
    console.log('PDF generator response:', responseJson);

    // Return a JSON response containing the direct download URL.
    if (responseJson.download_url) {
      return NextResponse.json({ 
        pdf_url: responseJson.download_url,
        message: 'PDF generated successfully using test data'
      });
    } else {
      console.error("PDF generator backend did not return a valid download URL.", responseJson);
      return NextResponse.json({ 
        error: 'Failed to generate PDF link',
        debug: 'No download_url in response',
        response: responseJson
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('Error in PDF export test route:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      debug: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
  return NextResponse.json({
    message: 'PDF Export Test Endpoint',
    description: 'Use POST method with citationFormat parameter to test PDF generation',
    samplePayload: {
      citationFormat: 'mla' // or 'apa'
    },
    sampleData: sampleCslItems
  });
}
