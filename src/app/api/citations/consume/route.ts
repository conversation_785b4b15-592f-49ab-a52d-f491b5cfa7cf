import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId' },
        { status: 400 }
      );
    }

    // Consume a citation
    const { data: success, error } = await supabase.rpc('consume_citation', {
      user_id: userId
    });

    if (error) {
      console.error('Error consuming citation:', error);
      return NextResponse.json(
        { error: 'Failed to consume citation' },
        { status: 500 }
      );
    }

    if (!success) {
      return NextResponse.json(
        { error: 'No citations available' },
        { status: 403 }
      );
    }

    // Get updated available citations
    const { data: availableCitations } = await supabase.rpc('get_available_citations', {
      user_id: userId
    });

    return NextResponse.json({
      success: true,
      availableCitations: availableCitations || 0
    });

  } catch (error) {
    console.error('Error in consume citation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
