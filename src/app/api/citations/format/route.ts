import { NextRequest, NextResponse } from 'next/server';

// 动态导入amplify_outputs.json
const getAmplifyOutputs = async () => {
  try {
    const outputs = await import('../../../../../amplify_outputs.json');
    return outputs.default || outputs;
  } catch (error) {
    console.error('Failed to load amplify outputs:', error);
    throw error;
  }
};

// 定义Lambda响应接口
interface LambdaResponse {
  output_type: 'html' | 'text' | 'pdf';
  data: any;
  citations?: any[];
  citation_count?: number;
  error?: string;
}

export async function POST(req: NextRequest) {
  console.log('FORMAT ROUTE HIT');
  
  try {
    // 1. 解析请求体
    const requestBody = await req.json();
    const { 
      citations, 
      citation_format, 
      html,
      title,
      content,
      plan
    } = requestBody;

    console.log('Format route received:', {
      hasCitations: !!citations,
      citationsLength: citations?.length,
      hasContent: !!content,
      citation_format,
      html,
      plan
    });

    let apiPayload: any = {};

    // 2. 决定是格式化模式还是完整生成模式
    if (citations && Array.isArray(citations) && citations.length > 0) {
      // 格式化现有引用模式
      console.log('Using format-only mode with', citations.length, 'citations');
      
      // 提取CSL数据
      const cslItems = citations.map((citation: any, index: number) => {
        if (citation?.csl_data) {
          return citation.csl_data;
        } else if (citation && typeof citation === 'object') {
          // 如果直接是CSL对象
          return citation;
        } else {
          console.warn(`Invalid citation at index ${index}:`, citation);
          return null;
        }
      }).filter(Boolean); // 过滤掉null值

      console.log('Extracted CSL items:', cslItems.length);

      if (cslItems.length === 0) {
        return NextResponse.json({ 
          error: 'No valid citations found to format' 
        }, { status: 400 });
      }

      apiPayload = {
        csl_items: cslItems,
        citation_format: (citation_format || 'mla').toLowerCase(),
        format_only: true,
        output_mode: html ? 'html' : 'text'
      };
      
    } else if (content && title) {
      // 完整生成模式
      console.log('Using full generation mode');
      
      if (!plan) {
        return NextResponse.json({ 
          error: 'Plan is required for full generation mode' 
        }, { status: 400 });
      }

      apiPayload = {
        title: title.trim(),
        content: content.trim(),
        citation_format: (citation_format || 'mla').toLowerCase(),
        plan: plan.toLowerCase(),
        output_mode: html ? 'html' : 'text',
        format_only: false,
        csl_items: []
      };
      
    } else {
      console.error('Invalid input provided:', { 
        hasCitations: !!citations, 
        hasContent: !!content, 
        hasTitle: !!title 
      });
      
      return NextResponse.json({ 
        error: 'Invalid input. Provide either "citations" array for formatting or "content" and "title" for generation.' 
      }, { status: 400 });
    }
    
    // 3. 获取Lambda函数URL
    const outputs = await getAmplifyOutputs();
    console.log('Amplify outputs loaded:', {
      hasCustom: !!outputs.custom,
      customKeys: outputs.custom ? Object.keys(outputs.custom) : []
    });
    
    const citationFunctionUrl = outputs.custom?.citationFunctionUrl;
    
    if (!citationFunctionUrl) {
      console.error('Citation function URL not found in amplify_outputs.json');
      console.error('Available outputs:', outputs);
      return NextResponse.json({ 
        error: 'Citation service not available - function URL not configured' 
      }, { status: 500 });
    }

    console.log('Using citation function URL:', citationFunctionUrl.substring(0, 50) + '...');
    console.log('Sending payload to Lambda:', {
      ...apiPayload,
      content: apiPayload.content ? `[${apiPayload.content.length} chars]` : undefined,
      csl_items: apiPayload.csl_items ? `[${apiPayload.csl_items.length} items]` : undefined
    });

    // 4. 调用Lambda函数
    const lambdaResponse = await fetch(citationFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(apiPayload),
    });

    console.log('Lambda response status:', lambdaResponse.status);
    console.log('Lambda response headers:', Object.fromEntries(lambdaResponse.headers.entries()));

    if (!lambdaResponse.ok) {
      const errorText = await lambdaResponse.text();
      console.error(`Lambda function returned ${lambdaResponse.status}:`, errorText);

      // 为不同的错误状态码提供用户友好的消息
      let userMessage = 'Citation formatting service is temporarily unavailable';
      if (lambdaResponse.status === 502) {
        userMessage = 'Citation formatting service is experiencing technical difficulties. Please refresh the page or contact IT support if the problem persists.';
      } else if (lambdaResponse.status === 500) {
        userMessage = 'Internal citation formatting error. Please try again or contact IT support.';
      } else if (lambdaResponse.status === 503) {
        userMessage = 'Citation formatting service is temporarily overloaded. Please try again in a few moments.';
      }

      return NextResponse.json({
        error: userMessage,
        details: `Service error (${lambdaResponse.status})`,
        technicalDetails: errorText
      }, { status: lambdaResponse.status });
    }

    // 5. 解析Lambda响应
    let responseJson: LambdaResponse;
    const responseText = await lambdaResponse.text();
    
    try {
      responseJson = JSON.parse(responseText) as LambdaResponse;
      console.log('Lambda response parsed:', {
        output_type: responseJson.output_type,
        hasData: !!responseJson.data,
        citation_count: responseJson.citation_count,
        hasError: !!responseJson.error,
        dataLength: typeof responseJson.data === 'string' ? responseJson.data.length : 'not string'
      });
    } catch (parseError) {
      console.error('Failed to parse Lambda response as JSON:', parseError);
      console.error('Raw response (first 500 chars):', responseText.substring(0, 500));
      
      return NextResponse.json({ 
        error: 'Invalid response from citation service',
        details: 'Response is not valid JSON'
      }, { status: 500 });
    }

    // 6. 检查Lambda返回的错误
    if (responseJson.error) {
      console.error('Lambda returned error:', responseJson.error);

      // 检查是否是依赖导入错误
      const isImportError = responseJson.error.includes('import failed') ||
                           responseJson.error.includes('No module named') ||
                           responseJson.error.includes('Critical dependency');

      const userMessage = isImportError
        ? 'Citation formatting service is experiencing configuration issues. Please refresh the page or contact IT support.'
        : 'Citation formatting failed due to a service error. Please try again or contact IT support if the problem persists.';

      return NextResponse.json({
        error: userMessage,
        details: 'Service configuration error',
        technicalDetails: responseJson.error
      }, { status: 500 });
    }

    // 7. 验证响应结构
    if (!responseJson.output_type || responseJson.data === undefined) {
      console.error('Invalid response structure from Lambda:', {
        hasOutputType: !!responseJson.output_type,
        hasData: responseJson.data !== undefined,
        actualData: responseJson.data
      });
      
      return NextResponse.json({ 
        error: 'Invalid response from citation service',
        details: 'Missing output_type or data field'
      }, { status: 500 });
    }
    
    // 8. 根据输出类型返回相应格式的响应
    console.log('About to process response data (format):', {
      output_type: responseJson.output_type,
      dataType: typeof responseJson.data,
      dataLength: typeof responseJson.data === 'string' ? responseJson.data.length : 'not string',
      dataPreview: typeof responseJson.data === 'string' ? responseJson.data.substring(0, 100) + '...' : responseJson.data,
      // citation_count: citationCount
    });

    const { output_type, data } = responseJson;
    const citationCount = responseJson.citation_count || 0;
    const citationing = responseJson.citations || [];

    switch (output_type) {
      case 'html':
        if (typeof data !== 'string') {
          console.error('Expected string data for HTML output, got:', typeof data);
          return NextResponse.json({ 
            error: 'Invalid HTML data from citation service' 
          }, { status: 500 });
        }
        
        return NextResponse.json({ 
          citation_html: data,
          citation_count: citationCount,
          citations: citationing
        });
        
      case 'text':
        if (typeof data !== 'string') {
          console.error('Expected string data for text output, got:', typeof data);
          return NextResponse.json({ 
            error: 'Invalid text data from citation service' 
          }, { status: 500 });
        }
        
        return NextResponse.json({ 
          citation: data,
          citation_count: citationCount,
          citations: citationing
        });
        
      case 'pdf':
        if (!data || !data.download_url) {
          console.error('PDF response missing download_url:', data);
          return NextResponse.json({ 
            error: 'PDF generation failed - no download URL provided' 
          }, { status: 500 });
        }
        
        return NextResponse.json({ 
          pdf_download_url: data.download_url,
          citation_count: citationCount,
          citations: citationing
        });
        
      default:
        console.error('Unknown output type from Lambda:', output_type);
        return NextResponse.json({ 
          error: 'Unknown response format from citation service',
          details: `Unexpected output_type: ${output_type}`
        }, { status: 500 });
    }

  } catch (error: any) {
    console.error('Unexpected error in format route:', error);
    console.error('Error stack:', error.stack);
    
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}