import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId' },
        { status: 400 }
      );
    }

    // Check if user has available citations
    const { data: availableCitations, error } = await supabase.rpc('get_available_citations', {
      user_id: userId
    });

    if (error) {
      console.error('Error checking citation limit:', error);
      return NextResponse.json(
        { error: 'Failed to check citation limit' },
        { status: 500 }
      );
    }

    const hasCitations = availableCitations > 0;

    return NextResponse.json({
      hasCitations,
      availableCitations: availableCitations || 0
    });

  } catch (error) {
    console.error('Error in check-limit:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
