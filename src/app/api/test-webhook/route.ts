import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    console.log('Test webhook received:', {
      method: request.method,
      headers: Object.fromEntries(request.headers.entries()),
      body: body.substring(0, 500) // First 500 chars
    });
    
    return NextResponse.json({ 
      received: true, 
      timestamp: new Date().toISOString(),
      bodyLength: body.length
    });
  } catch (error) {
    console.error('Test webhook error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ 
    status: 'Test webhook endpoint is working',
    timestamp: new Date().toISOString()
  });
} 