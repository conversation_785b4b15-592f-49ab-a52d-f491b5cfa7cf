"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { BookOpen, Mail, Eye, EyeOff, ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useAuth } from "@/contexts/AuthContext"
import { supabase } from "@/lib/supabase"
import { redirect, useRouter, useSearchParams } from "next/navigation"

export default function GetStartedClient() {
  const searchParams = useSearchParams();
  const initialIsSignUp = searchParams?.get('signin') === '1' ? false : true;
  const [isSignUp, setIsSignUp] = useState(initialIsSignUp);
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const { signInWithGoogle, signInWithMicrosoft, user } = useAuth()
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    firstName: "",
    lastName: ""
  })
  const router = useRouter()

  // Redirect logged-in users to dashboard
  useEffect(() => {
    if (user) {
      router.push("/dashboard")
    }
  }, [user, router])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    try {
      if (isSignUp) {
        if (formData.password !== formData.confirmPassword) {
          throw new Error("Passwords don't match")
        }

        const { error } = await supabase.auth.signUp({
          email: formData.email,
          password: formData.password,
          options: {
            data: {
              first_name: formData.firstName,
              last_name: formData.lastName,
            }
          }
        })

        if (error) throw error
        // Show success message or redirect
      } else {
        const { error } = await supabase.auth.signInWithPassword({
          email: formData.email,
          password: formData.password,
        })

        if (error) {
          // If user not found, show sign up option
          if (error.message && error.message.toLowerCase().includes("user not found")) {
            setError("No account found with this email. Would you like to create one?")
            setIsSignUp(true)
            setLoading(false)
            return
          }
          throw error
        }
        // Redirect to dashboard
      }
    } catch (error: any) {
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setLoading(true)
    setError("")
    try {
      await signInWithGoogle()
    } catch (error: any) {
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleMicrosoftSignIn = async () => {
    setLoading(true)
    setError("")
    try {
      await signInWithMicrosoft()
    } catch (error: any) {
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-4rem)] py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md border-amber-200 bg-white/90 backdrop-blur-sm">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-amber-900">
            {isSignUp ? "Create Your Account" : "Welcome Back"}
          </CardTitle>
          <CardDescription className="text-amber-700">
            {isSignUp ? "Start generating perfect citations in seconds" : "Sign in to continue citing with CiteAI"}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <Button
            onClick={handleGoogleSignIn}
            disabled={loading}
            variant="outline"
            className="w-full border-amber-300 text-amber-800 hover:bg-amber-50 bg-transparent"
          >
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
              <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
              <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
              <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
            </svg>
            {loading ? "Signing in..." : "Continue with Google"}
          </Button>

          <Button
            onClick={handleMicrosoftSignIn}
            disabled={loading}
            variant="outline"
            className="w-full border-amber-300 text-amber-800 hover:bg-amber-50 bg-transparent"
          >
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path fill="currentColor" d="M11.5 2.75h-8a.75.75 0 0 0-.75.75v7.5c0 .414.336.75.75.75h8a.75.75 0 0 0 .75-.75v-7.5a.75.75 0 0 0-.75-.75Z"/>
                <path fill="currentColor" d="M20.5 2.75h-8a.75.75 0 0 0-.75.75v7.5c0 .414.336.75.75.75h8a.75.75 0 0 0 .75-.75v-7.5a.75.75 0 0 0-.75-.75Z"/>
                <path fill="currentColor" d="M11.5 12.75h-8a.75.75 0 0 0-.75.75v7.5c0 .414.336.75.75.75h8a.75.75 0 0 0 .75-.75v-7.5a.75.75 0 0 0-.75-.75Z"/>
                <path fill="currentColor" d="M20.5 12.75h-8a.75.75 0 0 0-.75.75v7.5c0 .414.336.75.75.75h8a.75.75 0 0 0 .75-.75v-7.5a.75.75 0 0 0-.75-.75Z"/>
            </svg>
            {loading ? "Signing in..." : "Continue with Microsoft"}
          </Button>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full border-amber-200" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-amber-600">Or continue with email</span>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {isSignUp && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-amber-800">First Name</Label>
                  <Input id="firstName" name="firstName" type="text" required value={formData.firstName} onChange={handleInputChange} className="border-amber-200 focus:border-amber-500 focus:ring-amber-500" placeholder="John" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-amber-800">Last Name</Label>
                  <Input id="lastName" name="lastName" type="text" required value={formData.lastName} onChange={handleInputChange} className="border-amber-200 focus:border-amber-500 focus:ring-amber-500" placeholder="Doe" />
                </div>
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-amber-800">Email Address</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-amber-500" />
                <Input id="email" name="email" type="email" required value={formData.email} onChange={handleInputChange} className="pl-10 border-amber-200 focus:border-amber-500 focus:ring-amber-500" placeholder="<EMAIL>" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="password" className="text-amber-800">Password</Label>
              <div className="relative">
                <Input id="password" name="password" type={showPassword ? "text" : "password"} required value={formData.password} onChange={handleInputChange} className="pr-10 border-amber-200 focus:border-amber-500 focus:ring-amber-500" placeholder="Enter your password" />
                <button type="button" onClick={() => setShowPassword(!showPassword)} className="absolute right-3 top-3 text-amber-500 hover:text-amber-700">
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
            {isSignUp && (
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-amber-800">Confirm Password</Label>
                <Input id="confirmPassword" name="confirmPassword" type={showPassword ? "text" : "password"} required value={formData.confirmPassword} onChange={handleInputChange} className="border-amber-200 focus:border-amber-500 focus:ring-amber-500" placeholder="Confirm your password" />
              </div>
            )}
            <Button type="submit" disabled={loading} className="w-full bg-amber-600 hover:bg-amber-700 text-white">
              {loading ? "Processing..." : (isSignUp ? "Create Account" : "Sign In")}
            </Button>
          </form>

          <div className="text-center">
            <p className="text-sm text-amber-700">
              {isSignUp ? "Already have an account?" : "Don't have an account?"}{" "}
              <button onClick={() => setIsSignUp(!isSignUp)} className="font-medium text-amber-600 hover:text-amber-500 underline">
                {isSignUp ? "Sign in" : "Sign up"}
              </button>
            </p>
          </div>

          {!isSignUp && (
            <div className="text-center">
              <Link href="/forgot-password" className="text-sm text-amber-600 hover:text-amber-500 underline">
                Forgot your password?
              </Link>
            </div>
          )}

          {isSignUp && (
            <div className="text-center">
              <p className="text-xs text-amber-600">
                By creating an account, you agree to our{" "}
                <Link href="/terms" className="underline hover:text-amber-500">Terms of Service</Link>{" "}
                and{" "}
                <Link href="/privacy" className="underline hover:text-amber-500">Privacy Policy</Link>
              </p>
            </div>
          )}

          <div className="pt-4">
            <Link href="/dashboard">
              <Button variant="outline" className="w-full border-red-400 text-red-700 hover:bg-red-50">
                🚧 Skip Auth (Dev Only)
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
