import { Suspense } from 'react';
import Link from 'next/link';
import GetStartedClient from './get-started-client'; // Import the client component we just separated
import { BookOpen, ArrowLeft } from 'lucide-react';

// A simple loading UI to show while the client component is loading
function LoadingFallback() {
  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-4rem)]">
      <div className="text-amber-700 animate-pulse">Loading Form...</div>
    </div>
  );
}

// This is the main page component, which is a Server Component
export default function GetStartedPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50">
      {/* The Header is static and can be safely placed in the Server Component */}
      <header className="bg-white/80 backdrop-blur-md border-b border-amber-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <BookOpen className="h-8 w-8 text-amber-600" />
              <span className="text-2xl font-bold text-amber-900">CiteAI</span>
            </Link>
            <Link
              href="/"
              className="flex items-center space-x-2 text-amber-800 hover:text-amber-600 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Home</span>
            </Link>
          </div>
        </div>
      </header>

      {/* This is the key part!
        We wrap the dynamic client component with Suspense.
        The fallback prop specifies the component to show while GetStartedClient is loading.
      */}
      <Suspense fallback={<LoadingFallback />}>
        <GetStartedClient />
      </Suspense>
    </div>
  );
}

