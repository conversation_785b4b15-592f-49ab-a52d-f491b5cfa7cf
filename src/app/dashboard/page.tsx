"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { BookOpen, FileText, Trash2, User, LogOut, Plus, Search, ChevronRight, Check } from "lucide-react"
import Sidebar from "@/components/Sidebar"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/AuthContext"
import { supabase } from "@/lib/supabase"
import { usePlan } from '@/contexts/PlanContext';
import { SubscriptionManager } from '@/components/SubscriptionManager';

interface Document {
  id: string
  title: string
  content: string
  created_at: string
}

const mockDocs: Document[] = []

const mockPlan = {
  name: "Free",
  color: "bg-amber-900 text-white border-amber-900",
  description: "5 citations/month, MLA & APA, Basic support",
}

export default function Dashboard() {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()
  const { currentPlan, subscription, loading: planLoading, refreshSubscription } = usePlan();

  useEffect(() => {
    if (!loading && !user) {
      router.push("/get-started")
    }
  }, [user, loading, router])

  // Handle successful payment redirect from Stripe
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const canceled = urlParams.get('canceled');
    const sessionId = urlParams.get('session_id');
    const productId = urlParams.get('product_id');
    const showPricingParam = urlParams.get('showPricing');
    const showSubscriptionManagerParam = urlParams.get('showSubscriptionManager');
    
    // Handle microtransaction success from dashboard
    if (success === 'true' && sessionId && productId) {
      const source = urlParams.get('source');
      if (source === 'dashboard') {
        console.log('Microtransaction successful from dashboard, refreshing subscription data...');
        // Refresh the subscription data
        if (refreshSubscription) {
          refreshSubscription();
        }
        // Show success message
        setShowSuccessMessage(true);
        setTimeout(() => setShowSuccessMessage(false), 5000);
        // Clean up the URL
        window.history.replaceState({}, document.title, window.location.pathname);
      }
      // If source is 'document', let the document page handle it
    }
    
    // Handle subscription success
    if (success === 'true' && sessionId && !productId) {
      console.log('Subscription payment successful, refreshing subscription data...');
      // Refresh the subscription data
      if (refreshSubscription) {
        refreshSubscription();
      }
      // Show success message
      setShowSuccessMessage(true);
      setTimeout(() => setShowSuccessMessage(false), 5000);
      // Clean up the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
    
    // Handle canceled payment
    if (canceled === 'true') {
      console.log('Payment was canceled');
      // Clean up the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
    
    // Handle showPricing parameter
    if (showPricingParam === 'true') {
      setShowPricing(true);
      // Clean up the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
    
    // Handle showSubscriptionManager parameter
    if (showSubscriptionManagerParam === 'true') {
      setShowSubscriptionManager(true);
      // Clean up the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, [refreshSubscription]);

  const [search, setSearch] = useState("")
  const [showPricing, setShowPricing] = useState(false)
  const [docs, setDocs] = useState<Document[]>([])
  const [docsLoading, setDocsLoading] = useState(true)
  const [signOutLoading, setSignOutLoading] = useState(false)
  const [deletingDocId, setDeletingDocId] = useState<string | null>(null)
  const [showSubscriptionManager, setShowSubscriptionManager] = useState(false)
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)

  const planLabel = currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1) + ' Plan';

  // Fetch user's documents from Supabase (exclude deleted ones)
  useEffect(() => {
    const fetchDocs = async () => {
      if (!user) return
      const { data, error } = await supabase
        .from('documents')
        .select('id, title, content, created_at')
        .eq('user_id', user.id)
        .is('deleted_at', null) // Only fetch non-deleted documents
        .order('created_at', { ascending: false })
      if (error) {
        console.error('Error fetching documents:', error)
        return
      }
      setDocs(data || [])
      setDocsLoading(false)
    }
    fetchDocs()
  }, [user])

  // Create new doc in Supabase and redirect to editor
  const handleNewDoc = async () => {
    if (!user) return
    const { data, error } = await supabase
      .from('documents')
      .insert([
        {
          user_id: user.id,
          title: 'Untitled Document',
          content: '',
          status: 'processing',
        },
      ])
      .select()
      .single()
    if (error) {
      alert('Error creating document: ' + error.message)
      return
    }
    // Add new doc to the beginning of the list
    setDocs([data, ...docs])
    router.push(`/document/${data.id}`)
  }

  // Soft delete document (move to trash)
  const handleDeleteDoc = async (docId: string) => {
    if (!user) return

    try {
      setDeletingDocId(docId)

      // Use the soft delete function
      const { data, error } = await supabase.rpc('soft_delete_document', {
        doc_id: docId,
        user_uuid: user.id
      })

      if (error) {
        console.error('Error moving document to trash:', error)
        alert('Error moving document to trash: ' + error.message)
        return
      }

      if (!data) {
        alert('Document not found or already deleted')
        return
      }

      // Remove the document from the local state
      setDocs(docs.filter(doc => doc.id !== docId))
      console.log('Document moved to trash successfully')
    } catch (error) {
      console.error('Error moving document to trash:', error)
      alert('Error moving document to trash')
    } finally {
      setDeletingDocId(null)
    }
  }

  const filteredDocs = docs.filter(doc =>
    doc.title.toLowerCase().includes(search.toLowerCase()) ||
    (doc.content && doc.content.toLowerCase().includes(search.toLowerCase()))
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-amber-700">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect to get-started
  }

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 font-sans">
      <Sidebar signOutLoading={signOutLoading} signOut={signOut} user={user} currentPlan={currentPlan} />

      {/* Main Content */}
      <main className="ml-64 flex-1 p-10">
        <div className="flex items-center justify-between mb-8 gap-4 flex-wrap">
          <div className="flex items-center gap-2">
            <h1 className="text-3xl font-bold text-amber-900">Docs</h1>
            {/* Plan Pill */}
            <span
              className="inline-flex items-center px-2 py-1 rounded-full border border-amber-900 bg-amber-900 text-white text-xs font-semibold shadow-sm hover:shadow-md transition focus:outline-none focus:ring-2 focus:ring-amber-600"
              style={{ height: 28, minWidth: 70, maxWidth: 100 }}
              title="Current plan"
            >
              {planLabel}
              <ChevronRight className="h-3 w-3 ml-1 opacity-70" />
            </span>
          </div>
          <div className="flex items-center gap-2 flex-wrap">
            <Button className="bg-amber-600 hover:bg-amber-700 text-white font-semibold" onClick={handleNewDoc}>
              <Plus className="h-5 w-5 mr-2" /> New doc
            </Button>
            <Button
              className="bg-amber-400 hover:bg-amber-500 text-amber-900 font-bold border border-amber-900"
              onClick={() => setShowSubscriptionManager(true)}
              type="button"
            >
              Manage Subscription
            </Button>

          </div>
        </div>
        
        {/* Success Message */}
        {showSuccessMessage && (
          <div className="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            <div className="flex items-center gap-2">
              <Check className="h-5 w-5" />
              <span>Payment successful! Your subscription has been activated.</span>
            </div>
          </div>
        )}
        
        {/* Subscription Manager Modal */}
        {showSubscriptionManager && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
            <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-6xl w-full border-amber-200 border relative max-h-[90vh] overflow-y-auto">
              <button
                className="absolute top-3 right-3 text-amber-600 hover:text-amber-900 text-xl"
                onClick={() => setShowSubscriptionManager(false)}
                aria-label="Close"
              >
                ×
              </button>
              <SubscriptionManager />
            </div>
          </div>
        )}
        
        {/* Pricing Modal/Section - styled like landing page pricing */}
        {showPricing && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
            <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-3xl w-full border-amber-200 border relative">
              <button
                className="absolute top-3 right-3 text-amber-600 hover:text-amber-900 text-xl"
                onClick={() => setShowPricing(false)}
                aria-label="Close"
              >
                ×
              </button>
              <h2 className="text-3xl font-bold text-amber-900 mb-8 text-center">Simple, Transparent Pricing</h2>
              <div className="grid md:grid-cols-3 gap-8">
                {/* Free Plan */}
                <Card className="border-amber-200 bg-white/70 hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="text-amber-900">Free</CardTitle>
                    <CardContent className="text-amber-700">Perfect for trying out CiteAI</CardContent>
                    <div className="text-3xl font-bold text-amber-900 mt-2">
                      $0<span className="text-lg font-normal">/month</span>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2 mt-2">
                    <ul className="text-amber-800 text-sm space-y-1">
                      <li>5 citations per month</li>
                      <li>MLA & APA formats</li>
                      <li>Basic support</li>
                    </ul>
                  </CardContent>
                </Card>
                {/* Plus Plan */}
                <Card className="border-amber-400 bg-gradient-to-br from-amber-100 to-orange-100 hover:shadow-xl transition-shadow relative">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-amber-600 text-white rounded-full px-3 py-1 text-xs font-semibold shadow">Most Popular</div>
                  <CardHeader>
                    <CardTitle className="text-amber-900">Plus</CardTitle>
                    <CardContent className="text-amber-700">Ideal for active students</CardContent>
                    <div className="text-3xl font-bold text-amber-900 mt-2">
                      $15<span className="text-lg font-normal">/month</span>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2 mt-2">
                    <ul className="text-amber-800 text-sm space-y-1">
                      <li>Unlimited citations</li>
                      <li>All citation formats</li>
                      <li>Priority support</li>
                      <li>Export to Word/PDF</li>
                    </ul>
                    <Button className="w-full bg-amber-600 hover:bg-amber-700 text-white mt-2">Upgrade</Button>
                  </CardContent>
                </Card>
                {/* Pro Plan */}
                <Card className="border-amber-200 bg-white/70 hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="text-amber-900">Pro</CardTitle>
                    <CardContent className="text-amber-700">For researchers & institutions</CardContent>
                    <div className="text-3xl font-bold text-amber-900 mt-2">
                      $30<span className="text-lg font-normal">/month</span>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2 mt-2">
                    <ul className="text-amber-800 text-sm space-y-1">
                      <li>Everything in Student</li>
                      <li>Team collaboration</li>
                      <li>Advanced analytics</li>
                      <li>Custom integrations</li>
                    </ul>
                    <Button className="w-full bg-amber-100 text-amber-800 hover:bg-amber-200 border border-amber-300 mt-2">Upgrade</Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}
        <div className="flex items-center mb-6">
          <div className="relative w-full max-w-md">
            <input
              type="text"
              placeholder="Search docs"
              value={search}
              onChange={e => setSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-lg border border-amber-200 focus:border-amber-500 focus:ring-amber-500 bg-white text-amber-900 placeholder-amber-400"
            />
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-amber-400" />
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {docsLoading ? (
            <div className="col-span-full text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto mb-4"></div>
              <p className="text-amber-700">Loading documents...</p>
            </div>
          ) : filteredDocs.length === 0 ? (
            <div className="col-span-full text-center py-8">
              <BookOpen className="h-12 w-12 text-amber-300 mx-auto mb-4" />
              <p className="text-amber-600">No documents found</p>
              <p className="text-sm text-amber-500 mt-2">Check for typos or if the document is in the trash</p>
            </div>
          ) : (
            filteredDocs.map(doc => (
              <Card 
                key={doc.id} 
                className="border-amber-200 bg-white/90 hover:bg-white hover:shadow-xl transition-all duration-300 ease-in-out cursor-pointer transform hover:scale-[1.02]"
                onClick={() => router.push(`/document/${doc.id}`)}
              >
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs text-amber-500 font-medium">
                      {new Date(doc.created_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                    </span>
                    <span className="text-xs text-amber-600 border border-amber-300 rounded-full px-2 py-0.5">1</span>
                  </div>
                  <CardTitle className="text-amber-900 text-lg truncate">{doc.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-amber-700 text-sm truncate mb-4">
                    {doc.content ? doc.content.substring(0, 100) + (doc.content.length > 100 ? '...' : '') : 'No content yet'}
                  </p>
                  <div className="flex items-center justify-end gap-2">
                    <Button 
                      size="icon" 
                      variant="ghost" 
                      className="text-amber-500 hover:bg-amber-100"
                      onClick={(e) => {
                        e.stopPropagation() // Prevent card click
                        router.push(`/document/${doc.id}`)
                      }}
                    >
                      <svg width="20" height="20" fill="none" viewBox="0 0 24 24"><path d="M12 5v14m7-7H5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                    </Button>
                    <Button 
                      size="icon" 
                      variant="ghost" 
                      className="text-amber-500 hover:bg-amber-100"
                      disabled={deletingDocId === doc.id}
                      onClick={(e) => {
                        e.stopPropagation() // Prevent card click
                        if (confirm('Are you sure you want to move this document to trash? You can restore it later from the trash.')) {
                          handleDeleteDoc(doc.id)
                        }
                      }}
                    >
                      {deletingDocId === doc.id ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-amber-500"></div>
                      ) : (
                        <Trash2 className="h-5 w-5" />
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </main>
    </div>
  )
} 