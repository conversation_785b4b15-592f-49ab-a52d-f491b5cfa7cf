"use client"

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { MICROTRANSACTION_PRODUCTS } from '@/lib/stripe-client';
import { getStripe } from '@/lib/stripe-client';
import { useAuth } from '@/contexts/AuthContext';
import { usePlan } from '@/contexts/PlanContext';
import { ShoppingCart, CreditCard, Check } from 'lucide-react';

interface MicrotransactionPurchaseProps {
  documentId?: string;
  onPurchaseSuccess?: () => void;
}

export function MicrotransactionPurchase({ documentId, onPurchaseSuccess }: MicrotransactionPurchaseProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { refreshCitations } = usePlan();

  const handlePurchase = async (productId: string) => {
    if (!user) {
      setError('Please log in to purchase citations');
      return;
    }

    setLoading(productId);
    setError(null);

    try {
      // Create checkout session
      const response = await fetch('/api/stripe/create-microtransaction-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          userId: user.id,
          userEmail: user.email,
          documentId,
        }),
      });

      const { sessionId, error: sessionError } = await response.json();

      if (sessionError) {
        throw new Error(sessionError);
      }

      // Redirect to Stripe checkout
      const stripe = await getStripe();
      if (stripe) {
        const { error: stripeError } = await stripe.redirectToCheckout({
          sessionId,
        });

        if (stripeError) {
          throw new Error(stripeError.message);
        }
      }
    } catch (err: any) {
      setError(err.message || 'Failed to initiate purchase');
    } finally {
      setLoading(null);
    }
  };

  const products = Object.entries(MICROTRANSACTION_PRODUCTS);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          className="w-full bg-amber-50 border-amber-200 text-amber-900 hover:bg-amber-100"
        >
          <ShoppingCart className="h-4 w-4 mr-2" />
          Buy More Citations
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Purchase Additional Citations
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Need more citations? Purchase additional citations for your document.
          </p>
          
          {products.map(([key, product]) => (
            <div
              key={key}
              className="border border-gray-200 rounded-lg p-4 hover:border-amber-300 transition-colors relative"
            >
              {/* Best Value Tag for Citation Bundle */}
              {key === 'CITATION_BUNDLE' && (
                <div className="absolute -top-2 -right-2 bg-amber-600 text-white text-xs font-semibold px-2 py-1 rounded-full shadow-sm">
                  Best Value
                </div>
              )}
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h3 className="font-semibold text-gray-900">{product.name}</h3>
                  <p className="text-sm text-gray-600">{product.description}</p>
                </div>
                <div className="text-right">
                  <div className="font-bold text-lg text-amber-600">${product.price.toFixed(2)}</div>
                  <div className="text-xs text-gray-500">{product.citations} citation{product.citations > 1 ? 's' : ''}</div>
                </div>
              </div>
              
              <Button
                onClick={() => handlePurchase(product.id)}
                disabled={loading === product.id}
                className="w-full bg-amber-600 hover:bg-amber-700 text-white"
              >
                {loading === product.id ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Check className="h-4 w-4 mr-2" />
                )}
                {loading === product.id ? 'Processing...' : `Buy for $${product.price.toFixed(2)}`}
              </Button>
            </div>
          ))}
          
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
              {error}
            </div>
          )}
          
          <div className="text-xs text-gray-500 text-center">
            * Citations are added to your account and can be used for any document
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
