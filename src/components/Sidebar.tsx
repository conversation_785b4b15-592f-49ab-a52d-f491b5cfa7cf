import { <PERSON><PERSON><PERSON>, FileText, Trash2, User, LogOut, Crown, Zap, ShoppingCart } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { User as SupabaseUser } from '@supabase/supabase-js'
import { usePathname } from 'next/navigation'
import { useEffect, useState } from 'react';
import { usePlan } from '@/contexts/PlanContext';
import { useRouter } from 'next/navigation';
import { MicrotransactionPurchase } from './MicrotransactionPurchase';
import { SubscriptionManager } from './SubscriptionManager';
import { supabase } from '@/lib/supabase';

interface SidebarProps {
  signOutLoading?: boolean
  signOut?: () => Promise<void>
  user?: SupabaseUser | null
  currentPlan?: 'free' | 'plus' | 'pro'
}

export default function Sidebar({ signOutLoading = false, signOut, user }: SidebarProps) {
  const pathname = usePathname();
  const { currentPlan, availableCitations } = usePlan();
  const router = useRouter();
  const [showSubscriptionManager, setShowSubscriptionManager] = useState(false);
  const [documentCounts, setDocumentCounts] = useState({ active: 0, deleted: 0 });

  // Calculate citation usage for display
  const getCitationUsage = () => {
    if (currentPlan === 'pro') {
      return { available: '∞', total: '∞', percentage: 0 };
    } else if (currentPlan === 'plus') {
      const planLimit = 20;
      const used = Math.max(0, planLimit - availableCitations);
      return { 
        available: availableCitations, 
        total: planLimit, 
        percentage: Math.min(100, (used / planLimit) * 100) 
      };
    } else {
      const planLimit = 3;
      const used = Math.max(0, planLimit - availableCitations);
      return { 
        available: availableCitations, 
        total: planLimit, 
        percentage: Math.min(100, (used / planLimit) * 100) 
      };
    }
  };

  const citationUsage = getCitationUsage();

  // Fetch document counts
  useEffect(() => {
    const fetchDocumentCounts = async () => {
      if (!user) return;

      try {
        const { data, error } = await supabase.rpc('get_document_counts', {
          user_uuid: user.id
        });

        if (error) {
          console.error('Error fetching document counts:', error);
          return;
        }

        if (data && data.length > 0) {
          setDocumentCounts({
            active: data[0].active_count || 0,
            deleted: data[0].deleted_count || 0
          });
        }
      } catch (error) {
        console.error('Error fetching document counts:', error);
      }
    };

    fetchDocumentCounts();
  }, [user]);

  return (
    <aside className="fixed inset-y-0 left-0 z-40 w-64 bg-white/90 border-r border-amber-200 flex flex-col py-6 px-4">
      {/* Logo */}
      <div className="flex items-center mb-10 space-x-2">
        <BookOpen className="h-8 w-8 text-amber-600" />
        <span className="text-2xl font-bold text-amber-900">CiteAI</span>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-2">
        <Link href="/dashboard">
          <Button variant="ghost" className="w-full justify-between text-amber-900 group">
            <div className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              <span className={pathname.startsWith('/dashboard') ? 'font-bold text-amber-900' : 'font-normal'}>Docs</span>
            </div>
            {documentCounts.active > 0 && (
              <span className="text-xs bg-amber-100 text-amber-700 px-2 py-1 rounded-full">
                {documentCounts.active}
              </span>
            )}
          </Button>
        </Link>
        <Link href="/trash">
          <Button variant="ghost" className="w-full justify-between text-amber-800 group">
            <div className="flex items-center">
              <Trash2 className="h-5 w-5 mr-2" />
              <span className={pathname.startsWith('/trash') ? 'font-bold text-amber-900' : 'font-normal'}>Trash</span>
            </div>
            {documentCounts.deleted > 0 && (
              <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
                {documentCounts.deleted}
              </span>
            )}
          </Button>
        </Link>
        <Link href="/account">
          <Button variant="ghost" className="w-full justify-start text-amber-800 group">
            <User className="h-5 w-5 mr-2" />
            <span className={pathname.startsWith('/account') ? 'font-bold text-amber-900' : 'font-normal'}>Account</span>
          </Button>
        </Link>
      </nav>

      {/* Plan Details Card */}
      <div className="mb-6">
        <div className="rounded-xl border border-amber-200 bg-amber-50 p-6 shadow-sm">
          {/* Plan Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Crown className="h-5 w-5 text-amber-600" />
              <span className="text-lg font-semibold text-amber-900">
                {currentPlan?.charAt(0).toUpperCase() + currentPlan?.slice(1)} Plan
              </span>
            </div>
          </div>

          {/* Citation Usage */}
          {currentPlan !== 'pro' && (
            <div className="mb-4">
              <div className="flex justify-between text-sm text-amber-800 mb-2">
                <span>{citationUsage.available} citations available</span>
              </div>
              {/* Progress Bar */}
              <div className="w-full bg-amber-200 rounded-full h-2">
                <div 
                  className="bg-amber-600 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${citationUsage.percentage}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-4">
            {/* Buy More Citations - only for free and plus users */}
            {(currentPlan === 'free' || currentPlan === 'plus') && (
              <MicrotransactionPurchase 
                onPurchaseSuccess={() => {
                  // Refresh citations will be handled by the component
                }}
              />
            )}
            
            {/* Upgrade Plan Button */}
            <Button 
              className="w-full bg-amber-600 hover:bg-amber-700 text-white font-semibold"
              onClick={() => setShowSubscriptionManager(true)}
            >
              <Zap className="h-4 w-4 mr-2" />
              Upgrade Plan
            </Button>
          </div>
        </div>
      </div>

      {/* User Profile */}
      <div className="mt-auto">
        <div className="flex items-center space-x-3 p-3 rounded-lg bg-amber-50 border border-amber-100">
          <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center">
            <User className="h-5 w-5 text-amber-600" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-semibold text-amber-900 truncate">
              {user?.user_metadata?.first_name || user?.user_metadata?.name?.split(' ')[0] || ''} {user?.user_metadata?.last_name || user?.user_metadata?.name?.split(' ').slice(1).join(' ') || ''}
              {!(user?.user_metadata?.first_name || user?.user_metadata?.name) && 'User'}
            </div>
            <div className="text-xs text-amber-600">
              {currentPlan?.charAt(0).toUpperCase() + currentPlan?.slice(1)} Plan
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="sm"
            className="text-amber-600 hover:text-amber-800 hover:bg-amber-100"
            disabled={signOutLoading}
            onClick={async () => {
              try {
                if (signOut) await signOut();
                router.push('/get-started?signin=1');
              } catch (error) {}
            }}
          >
            <LogOut className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Subscription Manager Modal */}
      {showSubscriptionManager && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
          <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-6xl w-full border-amber-200 border relative max-h-[90vh] overflow-y-auto">
            <button
              className="absolute top-3 right-3 text-amber-600 hover:text-amber-900 text-xl"
              onClick={() => setShowSubscriptionManager(false)}
              aria-label="Close"
            >
              ×
            </button>
            <SubscriptionManager />
          </div>
        </div>
      )}
    </aside>
  )
} 