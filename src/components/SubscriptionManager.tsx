"use client"

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { usePlan } from '@/contexts/PlanContext';
import { STRIPE_PRODUCTS } from '@/lib/stripe-client';
import { getStripe } from '@/lib/stripe-client';
import { Check, Crown, Zap, CreditCard } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

export function SubscriptionManager() {
  const { user } = useAuth();
  const { currentPlan, subscription, loading, refreshSubscription } = usePlan();
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleUpgrade = async (planType: 'plus' | 'pro') => {
    if (!user) return;

    setIsLoading(true);
    try {
      const product = STRIPE_PRODUCTS[planType.toUpperCase() as keyof typeof STRIPE_PRODUCTS];
      
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: product.id,
          userId: user.id,
          planType,
          userEmail: user.email,
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        console.error('API Error:', data.error);
        alert('Failed to create checkout session: ' + data.error);
        return;
      }

      if (!data.sessionId) {
        console.error('No sessionId returned:', data);
        alert('Failed to create checkout session: No session ID returned');
        return;
      }

      console.log('Checkout session created:', data.sessionId);
      
      const stripe = await getStripe();
      if (stripe) {
        const { error } = await stripe.redirectToCheckout({ sessionId: data.sessionId });
        if (error) {
          console.error('Error redirecting to checkout:', error);
          alert('Error redirecting to checkout: ' + error.message);
        }
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      alert('Error creating checkout session: ' + error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription?.stripe_subscription_id) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/stripe/cancel-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: subscription.stripe_subscription_id,
        }),
      });

      if (response.ok) {
        await refreshSubscription();
        setIsDialogOpen(false); // Close the dialog after successful cancellation
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'free':
        return <Zap className="h-5 w-5" />;
      case 'plus':
        return <Crown className="h-5 w-5" />;
      case 'pro':
        return <CreditCard className="h-5 w-5" />;
      default:
        return <Zap className="h-5 w-5" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Plan Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getPlanIcon(currentPlan)}
            Current Plan: {STRIPE_PRODUCTS[currentPlan.toUpperCase() as keyof typeof STRIPE_PRODUCTS]?.name}
          </CardTitle>
          <CardDescription>
            {subscription ? (
              <div className="space-y-2">
                <div>Status: <Badge variant={subscription.status === 'active' ? 'default' : 'destructive'}>{subscription.status}</Badge></div>
                {subscription.current_period_end && (
                  <div>Renews: {new Date(subscription.current_period_end).toLocaleDateString()}</div>
                )}
              </div>
            ) : (
              'You are currently on the free plan'
            )}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Plan Comparison */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {Object.entries(STRIPE_PRODUCTS).map(([key, plan]) => {
          const isCurrentPlan = currentPlan === key.toLowerCase();
          const isUpgrade = !isCurrentPlan && currentPlan !== 'pro';
          
          return (
            <Card key={key} className={`relative ${isCurrentPlan ? 'ring-2 ring-amber-500' : ''}`}>
              {isCurrentPlan && (
                <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2" variant="default">
                  Current Plan
                </Badge>
              )}
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getPlanIcon(key.toLowerCase())}
                  {plan.name}
                </CardTitle>
                <CardDescription>
                  <span className="text-2xl font-bold">${plan.price}</span>
                  {plan.price > 0 ? '/month' : 'forever'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <Check className="h-4 w-4 text-green-500" />
                      {feature}
                    </li>
                  ))}
                </ul>
                
                {isCurrentPlan ? (
                  <Button disabled className="w-full">
                    Current Plan
                  </Button>
                ) : isUpgrade ? (
                  <Button 
                    onClick={() => handleUpgrade(key.toLowerCase() as 'plus' | 'pro')}
                    disabled={isLoading}
                    className="w-full bg-amber-600 hover:bg-amber-700"
                  >
                    {isLoading ? 'Loading...' : `Upgrade to ${plan.name}`}
                  </Button>
                ) : (
                  <Button disabled className="w-full" variant="outline">
                    Downgrade
                  </Button>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Subscription Management */}
      {subscription && subscription.status === 'active' && (
        <Card>
          <CardHeader>
            <CardTitle>Subscription Management</CardTitle>
            <CardDescription>
              Manage your current subscription
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive" disabled={isLoading}>
                  Cancel Subscription
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Cancel Subscription</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <p>Are you sure you want to cancel your subscription? You'll lose access to premium features at the end of your current billing period.</p>
                  <div className="flex gap-2">
                    <Button variant="destructive" onClick={handleCancelSubscription} disabled={isLoading}>
                      {isLoading ? 'Canceling...' : 'Yes, Cancel'}
                    </Button>
                    <Button variant="outline">No, Keep Subscription</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 