import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Check } from 'lucide-react';
import React from 'react';

interface PricingPlansProps {
  onClose?: () => void;
  modal?: boolean;
}

export const PricingPlans: React.FC<PricingPlansProps> = ({ onClose, modal }) => (
  <div className={modal ? 'fixed inset-0 z-50 flex items-center justify-center bg-black/40' : ''}>
    <div className={modal ? 'bg-white rounded-2xl shadow-2xl p-8 max-w-3xl w-full border-amber-200 border relative' : ''}>
      {modal && (
        <button
          className="absolute top-3 right-3 text-amber-600 hover:text-amber-900 text-xl"
          onClick={onClose}
          aria-label="Close"
        >
          ×
        </button>
      )}
      <h2 className="text-3xl font-bold text-amber-900 mb-8 text-center">Simple, Transparent Pricing</h2>
      <div className="grid md:grid-cols-3 gap-8">
        {/* Free Plan */}
        <Card className="border-amber-200 bg-white/70 hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="text-amber-900">Free</CardTitle>
            <CardContent className="text-amber-700">Perfect for trying out CiteAI</CardContent>
            <div className="text-3xl font-bold text-amber-900 mt-2">
              $0<span className="text-lg font-normal">/month</span>
            </div>
          </CardHeader>
          <CardContent className="space-y-2 mt-2">
            <ul className="text-amber-800 text-sm space-y-2">
              <li className="flex items-center gap-2"><Check className="text-green-600 w-5 h-5" />150 word excerpt limit</li>
                              <li className="flex items-center gap-2"><Check className="text-green-600 w-5 h-5" />3 citations per day</li>
            </ul>
          </CardContent>
        </Card>
        {/* Plus Plan */}
        <Card className="border-amber-400 bg-gradient-to-br from-amber-100 to-orange-100 hover:shadow-xl transition-shadow relative">
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-amber-600 text-white rounded-full px-3 py-1 text-xs font-semibold shadow">Most Popular</div>
          <CardHeader>
            <CardTitle className="text-amber-900">Plus</CardTitle>
            <CardContent className="text-amber-700">Ideal for active students</CardContent>
            <div className="text-3xl font-bold text-amber-900 mt-2">
              $15<span className="text-lg font-normal">/month</span>
            </div>
          </CardHeader>
          <CardContent className="space-y-2 mt-2">
            <ul className="text-amber-800 text-sm space-y-2">
              <li className="flex items-center gap-2"><Check className="text-green-600 w-5 h-5" />500 word excerpt limit</li>
              <li className="flex items-center gap-2"><Check className="text-green-600 w-5 h-5" />20 citations per day</li>
              <li className="flex items-center gap-2"><Check className="text-green-600 w-5 h-5" />No ads</li>
              <li className="flex items-center gap-2"><Check className="text-green-600 w-5 h-5" />Export to PDF</li>
            </ul>
            <Button className="w-full bg-amber-600 hover:bg-amber-700 text-white mt-2">Upgrade</Button>
          </CardContent>
        </Card>
        {/* Pro Plan */}
        <Card className="border-amber-200 bg-white/70 hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="text-amber-900">Pro</CardTitle>
            <CardContent className="text-amber-700">For passionate writers and researchers</CardContent>
            <div className="text-3xl font-bold text-amber-900 mt-2">
              $30<span className="text-lg font-normal">/month</span>
            </div>
          </CardHeader>
          <CardContent className="space-y-2 mt-2">
            <ul className="text-amber-800 text-sm space-y-2">
              <li className="flex items-center gap-2"><Check className="text-green-600 w-5 h-5" />Everything in Plus</li>
              <li className="flex items-center gap-2"><Check className="text-green-600 w-5 h-5" />Unlimited word excerpt</li>
              <li className="flex items-center gap-2"><Check className="text-green-600 w-5 h-5" />Unlimited citations</li>
            </ul>
            <Button className="w-full bg-amber-100 text-amber-800 hover:bg-amber-200 border border-amber-300 mt-2">Upgrade</Button>
          </CardContent>
        </Card>
      </div>
      <div className="mt-10 flex justify-center">
        <Card className="max-w-xl w-full border-amber-300 bg-white/90 shadow-md p-6 text-center">
          <CardHeader>
            <CardTitle className="text-amber-900 text-2xl">Institution or Bulk Usage?</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-amber-800 text-lg mb-4">For universities, news agencies, or any organizations interested in bulk access or custom integrations, please contact us:</p>
            <a href="mailto:<EMAIL>" className="text-amber-700 font-bold underline hover:text-amber-900"><EMAIL></a>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
); 