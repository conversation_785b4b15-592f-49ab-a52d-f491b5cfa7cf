"use client"

import React, { useState, useEffect } from 'react';
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface LoadingSpinnerProps {
  message?: string;
  timeout?: number;
  onTimeout?: () => void;
  showRetry?: boolean;
  onRetry?: () => void;
}

export function LoadingSpinner({ 
  message = "Loading...", 
  timeout = 10000, 
  onTimeout,
  showRetry = false,
  onRetry
}: LoadingSpinnerProps) {
  const [showTimeout, setShowTimeout] = useState(false);
  const [elapsed, setElapsed] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setElapsed(prev => {
        const newElapsed = prev + 100;
        if (newElapsed >= timeout && !showTimeout) {
          setShowTimeout(true);
          onTimeout?.();
        }
        return newElapsed;
      });
    }, 100);

    return () => clearInterval(timer);
  }, [timeout, onTimeout, showTimeout]);

  const handleRetry = () => {
    setShowTimeout(false);
    setElapsed(0);
    onRetry?.();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 flex items-center justify-center">
      <div className="text-center">
        <div className="relative mb-6">
          <Loader2 className="h-12 w-12 text-amber-600 animate-spin mx-auto" />
          {showTimeout && (
            <div className="absolute -top-2 -right-2">
              <AlertCircle className="h-6 w-6 text-red-500 animate-pulse" />
            </div>
          )}
        </div>
        
        <h2 className="text-xl font-semibold text-amber-900 mb-2">
          {showTimeout ? "Taking longer than expected..." : message}
        </h2>
        
        {showTimeout && (
          <div className="space-y-4">
            <p className="text-amber-700 text-sm">
              This might be due to a slow network connection or server issue.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              {showRetry && onRetry && (
                <Button 
                  onClick={handleRetry}
                  variant="outline"
                  className="border-amber-300 text-amber-700 hover:bg-amber-50"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              )}
              
              <Button 
                onClick={() => window.location.reload()}
                className="bg-amber-600 hover:bg-amber-700 text-white"
              >
                Reload Page
              </Button>
            </div>
          </div>
        )}
        
        {!showTimeout && (
          <div className="text-amber-600 text-sm">
            <div className="w-48 bg-amber-200 rounded-full h-1 mx-auto mt-4">
              <div 
                className="bg-amber-600 h-1 rounded-full transition-all duration-100"
                style={{ width: `${Math.min((elapsed / timeout) * 100, 100)}%` }}
              />
            </div>
            <p className="mt-2">
              {Math.round(elapsed / 1000)}s elapsed
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
