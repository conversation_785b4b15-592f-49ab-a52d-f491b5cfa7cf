"use client"

import React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { RotateCcw, Plus } from 'lucide-react';

interface CitationActionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onReplace: () => void;
  onAdd: () => void;
  loading?: boolean;
}

export function CitationActionDialog({
  isOpen,
  onClose,
  onReplace,
  onAdd,
  loading = false
}: CitationActionDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">Citation Action</DialogTitle>
          <DialogDescription className="text-center">
            Would you like to replace your existing citations, or add to them?
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex gap-4 mt-6">
          <Button
            onClick={onReplace}
            disabled={loading}
            variant="outline"
            className="flex-1 flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Replace
          </Button>
          
          <Button
            onClick={onAdd}
            disabled={loading}
            className="flex-1 flex items-center gap-2 bg-amber-600 hover:bg-amber-700"
          >
            <Plus className="h-4 w-4" />
            Add To
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
