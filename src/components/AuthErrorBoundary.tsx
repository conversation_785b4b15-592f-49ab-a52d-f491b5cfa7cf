"use client"

import React, { Component, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  retryCount: number;
}

export class AuthErrorBoundary extends Component<Props, State> {
  private retryTimeout?: NodeJS.Timeout;

  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, retryCount: 0 };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, retryCount: 0 };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Auth Error Boundary caught an error:', error, errorInfo);
  }

  handleRetry = () => {
    const { retryCount } = this.state;
    
    if (retryCount >= 3) {
      console.error('Max retry attempts reached');
      return;
    }

    this.setState(prevState => ({ 
      retryCount: prevState.retryCount + 1 
    }));

    // Clear error state after a short delay
    this.retryTimeout = setTimeout(() => {
      this.setState({ hasError: false, error: undefined });
    }, 1000);
  };

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center border border-amber-200">
            <div className="flex justify-center mb-4">
              <AlertTriangle className="h-16 w-16 text-amber-500" />
            </div>
            
            <h2 className="text-2xl font-bold text-amber-900 mb-4">
              Authentication Error
            </h2>
            
            <p className="text-amber-700 mb-6">
              There was a problem loading your authentication status. This might be due to a network issue or server problem.
            </p>

            {this.state.error && (
              <details className="mb-6 text-left">
                <summary className="text-sm text-amber-600 cursor-pointer hover:text-amber-800">
                  Technical Details
                </summary>
                <pre className="mt-2 text-xs text-amber-600 bg-amber-50 p-2 rounded overflow-auto">
                  {this.state.error.message}
                </pre>
              </details>
            )}

            <div className="space-y-3">
              <Button 
                onClick={this.handleRetry}
                disabled={this.state.retryCount >= 3}
                className="w-full bg-amber-600 hover:bg-amber-700 text-white"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                {this.state.retryCount >= 3 ? 'Max Retries Reached' : 'Try Again'}
              </Button>
              
              <Button 
                onClick={() => window.location.reload()}
                variant="outline"
                className="w-full border-amber-300 text-amber-700 hover:bg-amber-50"
              >
                Reload Page
              </Button>
            </div>

            {this.state.retryCount > 0 && (
              <p className="text-xs text-amber-500 mt-4">
                Retry attempt: {this.state.retryCount}/3
              </p>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
