"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Activity, Clock, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

interface PerformanceMetrics {
  authAttempts: number;
  fastCheckSuccess: number;
  fallbackCheckSuccess: number;
  cacheHits: number;
  timeouts: number;
  errors: number;
  averageResponseTime: number;
  lastAuthTime: number;
  circuitBreakerOpen: boolean;
}

export function AuthPerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    authAttempts: 0,
    fastCheckSuccess: 0,
    fallbackCheckSuccess: 0,
    cacheHits: 0,
    timeouts: 0,
    errors: 0,
    averageResponseTime: 0,
    lastAuthTime: 0,
    circuitBreakerOpen: false,
  });

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Listen for performance events
    const handlePerformanceEvent = (event: CustomEvent) => {
      const { type, data } = event.detail;
      
      setMetrics(prev => {
        const newMetrics = { ...prev };
        
        switch (type) {
          case 'auth_attempt':
            newMetrics.authAttempts++;
            break;
          case 'fast_check_success':
            newMetrics.fastCheckSuccess++;
            newMetrics.lastAuthTime = Date.now();
            break;
          case 'fallback_check_success':
            newMetrics.fallbackCheckSuccess++;
            newMetrics.lastAuthTime = Date.now();
            break;
          case 'cache_hit':
            newMetrics.cacheHits++;
            break;
          case 'timeout':
            newMetrics.timeouts++;
            break;
          case 'error':
            newMetrics.errors++;
            break;
          case 'circuit_breaker_open':
            newMetrics.circuitBreakerOpen = true;
            break;
          case 'circuit_breaker_close':
            newMetrics.circuitBreakerOpen = false;
            break;
          case 'response_time':
            newMetrics.averageResponseTime = data;
            break;
        }
        
        return newMetrics;
      });
    };

    window.addEventListener('auth-performance', handlePerformanceEvent as EventListener);
    
    return () => {
      window.removeEventListener('auth-performance', handlePerformanceEvent as EventListener);
    };
  }, []);

  const successRate = metrics.authAttempts > 0 
    ? ((metrics.fastCheckSuccess + metrics.fallbackCheckSuccess) / metrics.authAttempts * 100).toFixed(1)
    : '0';

  const cacheHitRate = metrics.authAttempts > 0
    ? (metrics.cacheHits / metrics.authAttempts * 100).toFixed(1)
    : '0';

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50 bg-blue-600 hover:bg-blue-700 text-white"
        size="sm"
      >
        <Activity className="h-4 w-4 mr-2" />
        Auth Monitor
      </Button>
    );
  }

  return (
    <Card className="fixed bottom-4 right-4 z-50 w-80 bg-white shadow-lg border">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">Auth Performance</CardTitle>
          <Button
            onClick={() => setIsVisible(false)}
            size="sm"
            variant="ghost"
            className="h-6 w-6 p-0"
          >
            ×
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center gap-1">
            <Activity className="h-3 w-3" />
            <span>Attempts: {metrics.authAttempts}</span>
          </div>
          <div className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3 text-green-500" />
            <span>Success: {successRate}%</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>Cache: {cacheHitRate}%</span>
          </div>
          <div className="flex items-center gap-1">
            <XCircle className="h-3 w-3 text-red-500" />
            <span>Errors: {metrics.errors}</span>
          </div>
        </div>

        <div className="space-y-1">
          <div className="flex justify-between text-xs">
            <span>Fast Check:</span>
            <Badge variant="outline" className="text-xs">
              {metrics.fastCheckSuccess}
            </Badge>
          </div>
          <div className="flex justify-between text-xs">
            <span>Fallback:</span>
            <Badge variant="outline" className="text-xs">
              {metrics.fallbackCheckSuccess}
            </Badge>
          </div>
          <div className="flex justify-between text-xs">
            <span>Timeouts:</span>
            <Badge variant="destructive" className="text-xs">
              {metrics.timeouts}
            </Badge>
          </div>
        </div>

        {metrics.circuitBreakerOpen && (
          <div className="flex items-center gap-1 text-xs text-orange-600">
            <AlertTriangle className="h-3 w-3" />
            <span>Circuit Breaker Open</span>
          </div>
        )}

        {metrics.averageResponseTime > 0 && (
          <div className="text-xs text-gray-600">
            Avg Response: {metrics.averageResponseTime.toFixed(0)}ms
          </div>
        )}

        {metrics.lastAuthTime > 0 && (
          <div className="text-xs text-gray-500">
            Last Auth: {new Date(metrics.lastAuthTime).toLocaleTimeString()}
          </div>
        )}

        <Button
          onClick={() => setMetrics({
            authAttempts: 0,
            fastCheckSuccess: 0,
            fallbackCheckSuccess: 0,
            cacheHits: 0,
            timeouts: 0,
            errors: 0,
            averageResponseTime: 0,
            lastAuthTime: 0,
            circuitBreakerOpen: false,
          })}
          size="sm"
          variant="outline"
          className="w-full text-xs"
        >
          Reset Metrics
        </Button>
      </CardContent>
    </Card>
  );
}
