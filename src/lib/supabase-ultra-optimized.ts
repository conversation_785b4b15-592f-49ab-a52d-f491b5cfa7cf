/**
 * Ultra-optimized Supabase client that completely avoids introspection queries
 */

import { createClient } from '@supabase/supabase-js';

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Create ultra-optimized Supabase client
export const supabaseUltraOptimized = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // Completely disable auto refresh
    autoRefreshToken: false,
    // Disable persistence to avoid storage queries
    persistSession: false,
    // Disable session detection in URL
    detectSessionInUrl: false,
    // No storage
    storage: undefined,
  },
  global: {
    // Ultra-aggressive timeout and minimal headers
    fetch: (url, options = {}) => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 2000); // 2 second timeout
      
      return fetch(url, {
        ...options,
        signal: controller.signal,
        // Minimal headers to avoid introspection
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          // Disable all introspection
          'Prefer': 'return=minimal,count=none',
          // Disable schema discovery
          'Accept-Profile': '',
        },
      }).finally(() => {
        clearTimeout(timeoutId);
      });
    },
  },
  db: {
    // Force specific schema to avoid discovery
    schema: 'public',
  },
  // Disable realtime to avoid connection overhead
  realtime: {
    // Realtime is disabled by default in this configuration
  },
});

// Ultra-fast auth check that bypasses all introspection
export async function ultraFastAuthCheck(): Promise<{ session: any; error?: string }> {
  try {
    // Use direct API call instead of Supabase client
    const response = await fetch(`${supabaseUrl}/auth/v1/user`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Accept': 'application/json',
        'Prefer': 'return=minimal',
      },
      signal: AbortSignal.timeout(1500), // 1.5 second timeout
    });

    if (!response.ok) {
      return { session: null, error: `HTTP ${response.status}` };
    }

    const data = await response.json();
    return { session: { user: data }, error: undefined };
  } catch (error) {
    return { 
      session: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Minimal health check that doesn't trigger introspection
export async function minimalHealthCheck(): Promise<{ isHealthy: boolean; error?: string }> {
  try {
    // Use a simple endpoint that doesn't require schema introspection
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'HEAD',
      headers: {
        'Accept': 'application/json',
        'Prefer': 'return=minimal',
      },
      signal: AbortSignal.timeout(1000), // 1 second timeout
    });

    return { 
      isHealthy: response.ok, 
      error: response.ok ? undefined : `HTTP ${response.status}` 
    };
  } catch (error) {
    return { 
      isHealthy: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Cached query helpers that avoid introspection
export class UltraOptimizedQueries {
  private static cache = new Map<string, { data: any; timestamp: number }>();
  private static CACHE_DURATION = 10 * 60 * 1000; // 10 minutes cache

  // Direct API calls that bypass PostgREST introspection
  static async getUserProfileDirect(userId: string) {
    const cacheKey = `user_profile_${userId}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }

    try {
      // Direct API call with minimal fields
      const response = await fetch(`${supabaseUrl}/rest/v1/users?id=eq.${userId}&select=id,subscription_tier,plan_type`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Accept': 'application/json',
          'Prefer': 'return=minimal',
        },
        signal: AbortSignal.timeout(1500),
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      const result = data[0] || null;
      
      if (result) {
        this.cache.set(cacheKey, { data: result, timestamp: Date.now() });
      }
      
      return result;
    } catch (error) {
      console.warn('Direct user profile query failed:', error);
      return null;
    }
  }

  static async getSubscriptionDirect(userId: string) {
    const cacheKey = `subscription_${userId}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }

    try {
      // Direct API call with minimal fields
      const response = await fetch(`${supabaseUrl}/rest/v1/user_subscriptions?user_id=eq.${userId}&select=id,plan_type,status,current_period_end`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Accept': 'application/json',
          'Prefer': 'return=minimal',
        },
        signal: AbortSignal.timeout(1500),
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      const result = data[0] || null;
      
      if (result) {
        this.cache.set(cacheKey, { data: result, timestamp: Date.now() });
      }
      
      return result;
    } catch (error) {
      console.warn('Direct subscription query failed:', error);
      return null;
    }
  }

  static clearCache() {
    this.cache.clear();
  }
}

// Circuit breaker for ultra-optimized queries
export class UltraCircuitBreaker {
  private static failures = 0;
  private static lastFailure = 0;
  private static isOpen = false;
  private static readonly MAX_FAILURES = 2;
  private static readonly COOLDOWN = 30000; // 30 seconds

  static canExecute(): boolean {
    if (!this.isOpen) return true;
    
    if (Date.now() - this.lastFailure > this.COOLDOWN) {
      this.isOpen = false;
      this.failures = 0;
      return true;
    }
    
    return false;
  }

  static recordSuccess() {
    this.failures = 0;
    this.isOpen = false;
  }

  static recordFailure() {
    this.failures++;
    this.lastFailure = Date.now();
    
    if (this.failures >= this.MAX_FAILURES) {
      this.isOpen = true;
      console.warn('Ultra circuit breaker opened');
    }
  }
}
