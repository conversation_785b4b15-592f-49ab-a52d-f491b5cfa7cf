/**
 * Supabase client configuration that completely disables introspection queries
 * This prevents the 45-second pg_catalog queries that cause timeouts
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Create a Supabase client that completely avoids introspection
export const supabaseNoIntrospection = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // Disable all auto-refresh to prevent background queries
    autoRefreshToken: false,
    // Disable session persistence to avoid storage queries
    persistSession: false,
    // Disable session detection in URL
    detectSessionInUrl: false,
    // No storage
    storage: undefined,
  },
  global: {
    // Custom fetch that prevents introspection
    fetch: (url, options = {}) => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 2000); // 2 second timeout
      
      // Modify URL to prevent introspection
      let modifiedUrl = url.toString();
      
      // Remove any introspection-related query parameters
      if (modifiedUrl.includes('select=')) {
        // Keep only essential fields
        modifiedUrl = modifiedUrl.replace(/select=[^&]*/g, 'select=*');
      }
      
      // Add headers to prevent introspection
      const headers = {
        ...options.headers,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        // Disable all introspection
        'Prefer': 'return=minimal,count=none',
        // Disable schema discovery
        'Accept-Profile': '',
        // Disable metadata queries
        'X-Client-Info': 'supabase-js-no-introspection',
      };
      
      return fetch(modifiedUrl, {
        ...options,
        signal: controller.signal,
        headers,
      }).finally(() => {
        clearTimeout(timeoutId);
      });
    },
  },
  db: {
    // Force specific schema to avoid discovery
    schema: 'public',
  },
  // Disable realtime to avoid connection overhead
  realtime: {
    // Realtime is disabled by default in this configuration
  },
});

// Direct API calls that bypass PostgREST introspection completely
export class NoIntrospectionAPI {
  private static baseUrl = supabaseUrl;
  private static anonKey = supabaseAnonKey;

  // Direct auth check without any introspection
  static async checkAuth(): Promise<{ user: any; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/auth/v1/user`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.anonKey}`,
          'Accept': 'application/json',
          'Prefer': 'return=minimal',
        },
        signal: AbortSignal.timeout(1500),
      });

      if (!response.ok) {
        return { user: null, error: `HTTP ${response.status}` };
      }

      const data = await response.json();
      return { user: data, error: undefined };
    } catch (error) {
      return { 
        user: null, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Direct user profile fetch without introspection
  static async getUserProfile(userId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/rest/v1/users?id=eq.${userId}&select=id,subscription_tier,plan_type`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.anonKey}`,
          'Accept': 'application/json',
          'Prefer': 'return=minimal',
        },
        signal: AbortSignal.timeout(1500),
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      return data[0] || null;
    } catch (error) {
      console.warn('Direct user profile fetch failed:', error);
      return null;
    }
  }

  // Direct subscription fetch without introspection
  static async getSubscription(userId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/rest/v1/user_subscriptions?user_id=eq.${userId}&select=id,plan_type,status,current_period_end`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.anonKey}`,
          'Accept': 'application/json',
          'Prefer': 'return=minimal',
        },
        signal: AbortSignal.timeout(1500),
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      return data[0] || null;
    } catch (error) {
      console.warn('Direct subscription fetch failed:', error);
      return null;
    }
  }

  // Health check that doesn't trigger introspection
  static async healthCheck(): Promise<{ isHealthy: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/rest/v1/`, {
        method: 'HEAD',
        headers: {
          'Accept': 'application/json',
          'Prefer': 'return=minimal',
        },
        signal: AbortSignal.timeout(1000),
      });

      return { 
        isHealthy: response.ok, 
        error: response.ok ? undefined : `HTTP ${response.status}` 
      };
    } catch (error) {
      return { 
        isHealthy: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}

// Export the no-introspection client as the main client
export default supabaseNoIntrospection;
