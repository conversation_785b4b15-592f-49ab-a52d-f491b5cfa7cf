/**
 * Supabase connection health check utilities
 */

import { supabase } from './supabase';

export interface HealthCheckResult {
  isHealthy: boolean;
  latency: number;
  error?: string;
  timestamp: number;
}

export class SupabaseHealthChecker {
  private static instance: SupabaseHealth<PERSON>hecker;
  private lastCheck: HealthCheckResult | null = null;
  private checkInterval: NodeJS.Timeout | null = null;

  static getInstance(): SupabaseHealthChecker {
    if (!SupabaseHealthChecker.instance) {
      SupabaseHealthChecker.instance = new SupabaseHealthChecker();
    }
    return SupabaseHealthChecker.instance;
  }

  /**
   * Perform a quick health check by pinging the Supabase instance
   */
  async checkHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Use Promise.race to implement timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Health check timeout')), 5000);
      });

      const queryPromise = supabase
        .from('users')
        .select('id')
        .limit(1);

      const { error } = await Promise.race([queryPromise, timeoutPromise]);

      const latency = Date.now() - startTime;
      
      if (error) {
        // Some errors are expected (like no rows found) and don't indicate health issues
        if (error.code === 'PGRST116' || error.message.includes('No rows found')) {
          return {
            isHealthy: true,
            latency,
            timestamp: Date.now()
          };
        }
        
        return {
          isHealthy: false,
          latency,
          error: error.message,
          timestamp: Date.now()
        };
      }

      this.lastCheck = {
        isHealthy: true,
        latency,
        timestamp: Date.now()
      };

      return this.lastCheck;
    } catch (error) {
      const latency = Date.now() - startTime;
      const result = {
        isHealthy: false,
        latency,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
      
      this.lastCheck = result;
      return result;
    }
  }

  /**
   * Get the last health check result
   */
  getLastCheck(): HealthCheckResult | null {
    return this.lastCheck;
  }

  /**
   * Check if the last health check is recent (within 30 seconds)
   */
  isLastCheckRecent(): boolean {
    if (!this.lastCheck) return false;
    return Date.now() - this.lastCheck.timestamp < 30000;
  }

  /**
   * Start periodic health checks
   */
  startPeriodicCheck(intervalMs: number = 60000): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.checkInterval = setInterval(() => {
      this.checkHealth().catch(error => {
        console.warn('Periodic health check failed:', error);
      });
    }, intervalMs);
  }

  /**
   * Stop periodic health checks
   */
  stopPeriodicCheck(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Check if Supabase is likely healthy based on recent checks
   */
  isLikelyHealthy(): boolean {
    if (!this.lastCheck) return true; // Assume healthy if no data
    if (!this.isLastCheckRecent()) return true; // Assume healthy if check is stale
    
    return this.lastCheck.isHealthy && this.lastCheck.latency < 2000; // Healthy if < 2s latency
  }
}

// Export singleton instance
export const supabaseHealth = SupabaseHealthChecker.getInstance();
