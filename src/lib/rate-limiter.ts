/**
 * Simple in-memory rate limiter for API endpoints
 * Prevents abuse by limiting requests per user per time window
 */

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

class RateLimiter {
  private requests: Map<string, RateLimitEntry> = new Map();
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) { // 15 minutes
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    
    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  /**
   * Check if a request should be allowed
   * @param identifier - Unique identifier (user ID, IP address, etc.)
   * @returns Object with allowed status and remaining requests
   */
  checkLimit(identifier: string): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now();
    const entry = this.requests.get(identifier);

    if (!entry || now > entry.resetTime) {
      // First request or window expired
      const resetTime = now + this.windowMs;
      this.requests.set(identifier, { count: 1, resetTime });
      return { allowed: true, remaining: this.maxRequests - 1, resetTime };
    }

    if (entry.count >= this.maxRequests) {
      // Rate limit exceeded
      return { allowed: false, remaining: 0, resetTime: entry.resetTime };
    }

    // Increment count
    entry.count++;
    this.requests.set(identifier, entry);
    return { allowed: true, remaining: this.maxRequests - entry.count, resetTime: entry.resetTime };
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.requests.entries()) {
      if (now > entry.resetTime) {
        this.requests.delete(key);
      }
    }
  }

  /**
   * Reset rate limit for a specific identifier
   * @param identifier - Unique identifier to reset
   */
  reset(identifier: string): void {
    this.requests.delete(identifier);
  }
}

// Create rate limiter instances for different endpoints
export const generalRateLimiter = new RateLimiter(100, 15 * 60 * 1000); // 100 requests per 15 minutes
export const pdfRateLimiter = new RateLimiter(10, 60 * 1000); // 10 PDF operations per minute
export const citationRateLimiter = new RateLimiter(50, 15 * 60 * 1000); // 50 citation operations per 15 minutes

/**
 * Middleware function to apply rate limiting to API routes
 * @param identifier - Unique identifier for the user/client
 * @param limiter - Rate limiter instance to use
 * @returns Rate limit result
 */
export function applyRateLimit(identifier: string, limiter: RateLimiter = generalRateLimiter) {
  return limiter.checkLimit(identifier);
}

/**
 * Get client identifier from request (user ID or IP address)
 * @param req - Next.js request object
 * @param userId - Optional user ID if authenticated
 * @returns Unique identifier for rate limiting
 */
export function getClientIdentifier(req: Request, userId?: string): string {
  if (userId) {
    return `user:${userId}`;
  }
  
  // Fallback to IP address for unauthenticated requests
  const forwarded = req.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] : req.headers.get('x-real-ip') || 'unknown';
  return `ip:${ip}`;
}
