/**
 * Optimized Supabase client with aggressive timeouts and query optimization
 */

import { createClient } from '@supabase/supabase-js';

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Create optimized Supabase client
export const supabaseOptimized = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // Disable auto refresh to prevent background queries
    autoRefreshToken: false,
    // Reduce persistence to minimize storage queries
    persistSession: true,
    // Disable detect session in URL to prevent extra checks
    detectSessionInUrl: false,
    // Use minimal storage
    storage: undefined,
  },
  global: {
    // Set aggressive timeout
    fetch: (url, options = {}) => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout
      
      return fetch(url, {
        ...options,
        signal: controller.signal,
        // Add headers to minimize introspection
        headers: {
          ...options.headers,
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          // Disable schema introspection
          'Prefer': 'return=minimal',
        },
      }).finally(() => {
        clearTimeout(timeoutId);
      });
    },
  },
  db: {
    // Disable schema introspection
    schema: 'public',
  },
});

// Optimized query helpers
export class OptimizedQueries {
  private static cache = new Map<string, { data: any; timestamp: number }>();
  private static CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  static async getUserProfile(userId: string) {
    const cacheKey = `user_profile_${userId}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }

    try {
      // Use Promise.race to implement timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Query timeout')), 2000);
      });

      const queryPromise = supabaseOptimized
        .from('users')
        .select('id, subscription_tier, plan_type')
        .eq('id', userId)
        .single();

      const { data, error } = await Promise.race([queryPromise, timeoutPromise]);

      if (error) {
        console.warn('User profile query failed:', error.message);
        return null;
      }

      this.cache.set(cacheKey, { data, timestamp: Date.now() });
      return data;
    } catch (error) {
      console.warn('User profile query error:', error);
      return null;
    }
  }

  static async getSubscription(userId: string) {
    const cacheKey = `subscription_${userId}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }

    try {
      // Use Promise.race to implement timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Query timeout')), 2000);
      });

      const queryPromise = supabaseOptimized
        .from('user_subscriptions')
        .select('id, plan_type, status, current_period_end')
        .eq('user_id', userId)
        .single();

      const { data, error } = await Promise.race([queryPromise, timeoutPromise]);

      if (error) {
        console.warn('Subscription query failed:', error.message);
        return null;
      }

      this.cache.set(cacheKey, { data, timestamp: Date.now() });
      return data;
    } catch (error) {
      console.warn('Subscription query error:', error);
      return null;
    }
  }

  static clearCache() {
    this.cache.clear();
  }

  static clearUserCache(userId: string) {
    this.cache.delete(`user_profile_${userId}`);
    this.cache.delete(`subscription_${userId}`);
  }
}

// Fast auth session check with minimal queries
export async function fastAuthCheck() {
  try {
    const { data: { session }, error } = await supabaseOptimized.auth.getSession();
    
    if (error) {
      console.warn('Fast auth check failed:', error.message);
      return { session: null, error: error.message };
    }

    return { session, error: null };
  } catch (error) {
    console.warn('Fast auth check error:', error);
    return { session: null, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Health check with minimal impact
export async function quickHealthCheck() {
  try {
    // Use Promise.race to implement timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Health check timeout')), 1000);
    });

    const queryPromise = supabaseOptimized
      .from('users')
      .select('id')
      .limit(1);

    const { error } = await Promise.race([queryPromise, timeoutPromise]);

    return {
      isHealthy: !error || error.code === 'PGRST116', // No rows found is OK
      error: error?.message,
      latency: 0 // We don't measure latency for quick checks
    };
  } catch (error) {
    return {
      isHealthy: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      latency: 0
    };
  }
}
