// Client-side Stripe configuration
export const getStripe = async () => {
  if (typeof window !== 'undefined') {
    const { loadStripe } = await import('@stripe/stripe-js');
    return loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
  }
  return null;
};

// Stripe product/price IDs - replace with your actual IDs from Stripe dashboard
export const STRIPE_PRODUCTS = {
  FREE: {
    id: 'price_1Rp17LIco7dd9lvI3E4nk8XZ',
    name: 'Free',
    price: 0,
    wordLimit: 150,
    features: ['Basic citation generation', 'MLA & APA formats']
  },
  PLUS: {
    id: 'price_1RpChsIco7dd9lvIEjwK9WOi',
    name: 'Plus',
    price: 15.00,
    wordLimit: 500,
    features: ['Enhanced citation generation', 'PDF export', 'Priority support']
  },
  PRO: {
    id: 'price_1Rp0tfIco7dd9lvIjZMrsRmy',
    name: 'Pro',
    price: 30.00,
    wordLimit: Infinity,
    features: ['Everything in Plus', 'Unlimited citations']
  }
} as const;

// Microtransaction products
export const MICROTRANSACTION_PRODUCTS = {
  SINGLE_CITATION: {
    id: 'prod_SkWAHuhEXBhxIr',
    name: '1 Extra Citation',
    price: 2.50,
    citations: 1,
    description: 'Get 1 additional citation for your document'
  },
  CITATION_BUNDLE: {
    id: 'prod_SkWBUo86980z7K',
    name: 'Citation Bundle (3 citations)',
    price: 5.00,
    citations: 3,
    description: 'Buy 2 get 1 free - 3 citations for $5'
  }
} as const;

export type StripeProductType = keyof typeof STRIPE_PRODUCTS;
export type MicrotransactionProductType = keyof typeof MICROTRANSACTION_PRODUCTS; 