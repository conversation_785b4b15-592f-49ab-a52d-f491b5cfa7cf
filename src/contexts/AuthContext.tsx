"use client"

import React, { createContext, useContext, useEffect, useState, useRef, useCallback } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { supabaseOptimized, fastAuth<PERSON>heck, quickHealthCheck } from '@/lib/supabase-optimized'
import { supabaseUltraOptimized, ultraFastAuthCheck, minimalHealthCheck, UltraCircuitBreaker } from '@/lib/supabase-ultra-optimized'
import { LoadingSpinner } from '@/components/LoadingSpinner'
import { supabaseHealth } from '@/lib/supabase-health'

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signInWithGoogle: () => Promise<void>
  signInWithMicrosoft: () => Promise<void>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Constants for timeout and retry logic
const AUTH_TIMEOUT = 3000 // 3 seconds - aggressive timeout
const FALLBACK_TIMEOUT = 5000 // 5 seconds for fallback
const MAX_RETRIES = 2 // Reduced retries
const RETRY_DELAY = 500 // 500ms between retries
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes cache

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  
  // Refs to track state and prevent race conditions
  const isInitialized = useRef(false)
  const profileChecked = useRef(new Set<string>())
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const retryCount = useRef(0)
  const circuitBreakerOpen = useRef(false)
  const lastFailureTime = useRef(0)
  const failureCount = useRef(0)

  // Cache management
  const getCachedAuth = useCallback(() => {
    try {
      const cached = localStorage.getItem('citeai_auth_cache');
      if (!cached) return null;
      
      const { data, timestamp } = JSON.parse(cached);
      if (Date.now() - timestamp > CACHE_DURATION) {
        localStorage.removeItem('citeai_auth_cache');
        return null;
      }
      
      return data;
    } catch {
      return null;
    }
  }, []);

  const setCachedAuth = useCallback((authData: any) => {
    try {
      localStorage.setItem('citeai_auth_cache', JSON.stringify({
        data: authData,
        timestamp: Date.now()
      }));
    } catch {
      // Ignore cache errors
    }
  }, []);

  // Circuit breaker logic
  const shouldAttemptRequest = useCallback(() => {
    if (!circuitBreakerOpen.current) return true;
    
    // Reset circuit breaker after 30 seconds
    if (Date.now() - lastFailureTime.current > 30000) {
      circuitBreakerOpen.current = false;
      failureCount.current = 0;
      return true;
    }
    
    return false;
  }, []);

  const recordFailure = useCallback(() => {
    failureCount.current++;
    lastFailureTime.current = Date.now();
    
    // Open circuit breaker after 3 failures
    if (failureCount.current >= 3) {
      circuitBreakerOpen.current = true;
      console.warn('Circuit breaker opened due to repeated failures');
    }
  }, []);

  const recordSuccess = useCallback(() => {
    failureCount.current = 0;
    circuitBreakerOpen.current = false;
  }, []);

  // Function to ensure user profile exists in public.users table
  const ensureUserProfile = useCallback(async (user: User): Promise<void> => {
    // Skip if already checked for this user
    if (profileChecked.current.has(user.id)) {
      console.log('User profile already checked for:', user.id);
      return;
    }

    try {
      console.log('Checking if user profile exists for:', user.id);
      
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        timeoutRef.current = setTimeout(() => {
          reject(new Error('Profile check timeout'));
        }, AUTH_TIMEOUT);
      });

      const profilePromise = supabase
        .from('users')
        .select('id')
        .eq('id', user.id)
        .single();

      const { data: existingUser, error: fetchError } = await Promise.race([
        profilePromise,
        timeoutPromise
      ]);

      // Clear timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      if (fetchError && fetchError.code === 'PGRST116') {
        // User profile doesn't exist - this should be handled by the trigger
        console.log('User profile not found - trigger should handle this automatically');
        // Don't try to manually create it - let the trigger handle it
      } else if (fetchError) {
        console.error('Error fetching user profile:', fetchError);
        throw fetchError;
      } else {
        console.log('User profile already exists');
      }

      // Mark as checked
      profileChecked.current.add(user.id);
    } catch (error) {
      console.error('Error checking user profile:', error);
      // Don't throw - this shouldn't block the auth flow
    }
  }, []);

  // Ultra-fast session check that completely avoids introspection queries
  const ultraFastSessionCheck = useCallback(async (): Promise<{ session: Session | null; error?: string }> => {
    const startTime = Date.now();
    
    try {
      // Check circuit breaker first
      if (!UltraCircuitBreaker.canExecute()) {
        console.warn('Ultra circuit breaker is open, skipping auth check');
        return { session: null, error: 'Circuit breaker open' };
      }

      // Use ultra-optimized client that bypasses all introspection
      const result = await ultraFastAuthCheck();
      
      const responseTime = Date.now() - startTime;
      
      if (result.session && !result.error) {
        UltraCircuitBreaker.recordSuccess();
        window.dispatchEvent(new CustomEvent('auth-performance', {
          detail: { type: 'ultra_fast_check_success', data: responseTime }
        }));
      } else {
        UltraCircuitBreaker.recordFailure();
        window.dispatchEvent(new CustomEvent('auth-performance', {
          detail: { type: 'error' }
        }));
      }
      
      return { session: result.session, error: result.error || undefined };
    } catch (error) {
      UltraCircuitBreaker.recordFailure();
      window.dispatchEvent(new CustomEvent('auth-performance', {
        detail: { type: 'error' }
      }));
      return { session: null, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }, []);

  // Fast session check with aggressive timeout using optimized client
  const fastSessionCheck = useCallback(async (): Promise<{ session: Session | null; error?: string }> => {
    const startTime = Date.now();
    
    try {
      // Use optimized client for faster response
      const result = await fastAuthCheck();
      
      const responseTime = Date.now() - startTime;
      
      if (result.session && !result.error) {
        window.dispatchEvent(new CustomEvent('auth-performance', {
          detail: { type: 'fast_check_success', data: responseTime }
        }));
      } else {
        window.dispatchEvent(new CustomEvent('auth-performance', {
          detail: { type: 'error' }
        }));
      }
      
      return { session: result.session, error: result.error || undefined };
    } catch (error) {
      window.dispatchEvent(new CustomEvent('auth-performance', {
        detail: { type: 'error' }
      }));
      return { session: null, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }, []);

  // Fallback session check with longer timeout using regular client
  const fallbackSessionCheck = useCallback(async (): Promise<{ session: Session | null; error?: string }> => {
    try {
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Fallback session timeout')), FALLBACK_TIMEOUT);
      });

      const sessionPromise = supabase.auth.getSession();
      const { data: { session }, error } = await Promise.race([sessionPromise, timeoutPromise]);

      return { session, error: error?.message };
    } catch (error) {
      return { session: null, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }, []);

  // Main session handler with parallel strategies
  const handleSessionWithTimeout = useCallback(async (retry = false): Promise<void> => {
    if (retry) {
      retryCount.current++;
      if (retryCount.current > MAX_RETRIES) {
        console.error('Max retries reached for session check');
        setLoading(false);
        return;
      }
      console.log(`Retrying session check (attempt ${retryCount.current})`);
    }

    // Check circuit breaker
    if (!shouldAttemptRequest()) {
      console.warn('Circuit breaker is open, using cached auth or skipping');
      const cached = getCachedAuth();
      if (cached) {
        setSession(cached.session);
        setUser(cached.user);
        setLoading(false);
        return;
      }
      setLoading(false);
      return;
    }

    try {
      // Record auth attempt
      window.dispatchEvent(new CustomEvent('auth-performance', {
        detail: { type: 'auth_attempt' }
      }));

      // Try ultra-fast check first (completely avoids introspection)
      console.log('Attempting ultra-fast session check...');
      const ultraFastResult = await ultraFastSessionCheck();
      
      if (ultraFastResult.session && !ultraFastResult.error) {
        console.log('Ultra-fast session check succeeded');
        setSession(ultraFastResult.session);
        setUser(ultraFastResult.session?.user ?? null);
        
        // Cache the result
        setCachedAuth({
          session: ultraFastResult.session,
          user: ultraFastResult.session?.user ?? null
        });
        
        recordSuccess();
        
        // Profile check in background (non-blocking)
        if (ultraFastResult.session?.user) {
          ensureUserProfile(ultraFastResult.session.user).catch(console.error);
        }
        
        setLoading(false);
        return;
      }

      // If ultra-fast check failed, try regular fast check
      console.log('Ultra-fast check failed, trying regular fast check...');
      const fastResult = await fastSessionCheck();
      
      if (fastResult.session && !fastResult.error) {
        console.log('Fast session check succeeded');
        setSession(fastResult.session);
        setUser(fastResult.session?.user ?? null);
        
        // Cache the result
        setCachedAuth({
          session: fastResult.session,
          user: fastResult.session?.user ?? null
        });
        
        recordSuccess();
        
        // Profile check in background (non-blocking)
        if (fastResult.session?.user) {
          ensureUserProfile(fastResult.session.user).catch(console.error);
        }
        
        setLoading(false);
        return;
      }

      // If fast check failed, try fallback
      console.log('Fast check failed, trying fallback...');
      const fallbackResult = await fallbackSessionCheck();
      
      if (fallbackResult.session && !fallbackResult.error) {
        console.log('Fallback session check succeeded');
        setSession(fallbackResult.session);
        setUser(fallbackResult.session?.user ?? null);
        
        // Cache the result
        setCachedAuth({
          session: fallbackResult.session,
          user: fallbackResult.session?.user ?? null
        });
        
        recordSuccess();
        
        // Profile check in background (non-blocking)
        if (fallbackResult.session?.user) {
          ensureUserProfile(fallbackResult.session.user).catch(console.error);
        }
        
        setLoading(false);
        return;
      }

      // Both checks failed
      console.error('Both fast and fallback session checks failed');
      recordFailure();
      
      // Try cached data as last resort
      const cached = getCachedAuth();
      if (cached) {
        console.log('Using cached auth data');
        setSession(cached.session);
        setUser(cached.user);
        setLoading(false);
        return;
      }

      // No cached data, set loading to false
      setLoading(false);
      
    } catch (error) {
      console.error('Session handling error:', error);
      recordFailure();
      
      // Try cached data as fallback
      const cached = getCachedAuth();
      if (cached) {
        console.log('Using cached auth data after error');
        setSession(cached.session);
        setUser(cached.user);
      }
      
      setLoading(false);
    }
  }, [fastSessionCheck, fallbackSessionCheck, shouldAttemptRequest, getCachedAuth, setCachedAuth, recordSuccess, recordFailure, ensureUserProfile]);

  useEffect(() => {
    // Prevent multiple initializations
    if (isInitialized.current) {
      return;
    }
    isInitialized.current = true;

    // Try cached auth first for instant loading
    const cached = getCachedAuth();
    if (cached) {
      console.log('Using cached auth data for instant loading');
      setSession(cached.session);
      setUser(cached.user);
      setLoading(false);
      
      // Record cache hit
      window.dispatchEvent(new CustomEvent('auth-performance', {
        detail: { type: 'cache_hit' }
      }));
      
      // Validate cached data in background
      handleSessionWithTimeout();
    } else {
      // No cached data, do fresh auth check
      handleSessionWithTimeout();
    }

    // Start periodic health checks (less frequent)
    supabaseHealth.startPeriodicCheck(120000); // Check every 2 minutes

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state change:', event, session?.user?.id);
      
      // Only update state if session actually changed
      if (session?.user?.id !== user?.id) {
        setSession(session);
        setUser(session?.user ?? null);
        
        // Cache the new session
        setCachedAuth({
          session,
          user: session?.user ?? null
        });
        
      if (session?.user) {
          // Profile check in background (non-blocking)
          ensureUserProfile(session.user).catch(console.error);
        }
      }

      setLoading(false);
    });

    // Optimized visibility change handler - use fast check
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !loading) {
        console.log('Tab became visible, doing fast session check...');
        
        // Use ultra-fast check for visibility change
        ultraFastSessionCheck().then(({ session, error }) => {
          if (session && !error && session?.user?.id !== user?.id) {
            console.log('Session changed on visibility change');
            setSession(session);
            setUser(session?.user ?? null);
            
            // Cache the session
            setCachedAuth({
              session,
              user: session?.user ?? null
            });
            
          if (session?.user) {
              ensureUserProfile(session.user).catch(console.error);
            }
          }
        }).catch(error => {
          console.error('Error checking session on visibility change:', error);
        });
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      subscription.unsubscribe();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      
      // Stop health checks
      supabaseHealth.stopPeriodicCheck();
      
      // Clear any pending timeouts
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [handleSessionWithTimeout, ultraFastSessionCheck, getCachedAuth, setCachedAuth, ensureUserProfile, user?.id, loading]);

  const signInWithGoogle = async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/dashboard`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          }
        }
      })
      if (error) throw error
    } catch (error) {
      console.error('Error signing in with Google:', error)
      throw error
    }
  }

  const signInWithMicrosoft = async () => {
    try {
      console.log('Starting Microsoft OAuth flow...')
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'azure',
        options: {
          redirectTo: `${window.location.origin}/dashboard`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
          scopes: 'email profile openid'
        }
      })
      
      if (error) {
        console.error('Microsoft OAuth error:', error)
        throw error
      }
      
      console.log('Microsoft OAuth initiated successfully:', data)
    } catch (error) {
      console.error('Error signing in with Microsoft:', error)
      throw error
    }
  }

  const signOut = async () => {
    try {
      console.log('Signing out...')
      const { error } = await supabase.auth.signOut()
      if (error) {
        console.error('Supabase sign out error:', error)
        throw error
      }
      console.log('Sign out successful')
      // Clear local state immediately
      setUser(null)
      setSession(null)
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    }
  }

  const value = {
    user,
    session,
    loading,
    signInWithGoogle,
    signInWithMicrosoft,
    signOut,
  }

  // Show loading spinner with aggressive timeout protection
  if (loading) {
    return (
      <LoadingSpinner 
        message="Initializing authentication..."
        timeout={AUTH_TIMEOUT + 1000} // 4 seconds total
        onTimeout={() => {
          console.error('Auth initialization timeout - using fallback');
          setLoading(false);
        }}
        showRetry={true}
        onRetry={() => {
          console.log('Retrying auth initialization...');
          setLoading(true);
          handleSessionWithTimeout();
        }}
      />
    );
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 