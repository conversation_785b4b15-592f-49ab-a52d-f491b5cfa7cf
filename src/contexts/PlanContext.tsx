"use client"
import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from './AuthContext';

export type PlanType = 'free' | 'plus' | 'pro';

interface Subscription {
  id: string;
  user_id: string;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  plan_type: PlanType;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid';
  current_period_start: string | null;
  current_period_end: string | null;
}

interface PlanContextType {
  currentPlan: PlanType;
  subscription: Subscription | null;
  loading: boolean;
  refreshSubscription: () => Promise<void>;
  availableCitations: number;
  refreshCitations: () => Promise<void>;
}

const PlanContext = createContext<PlanContextType | undefined>(undefined);

export const PlanProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [currentPlan, setCurrentPlan] = useState<PlanType>('free');
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [availableCitations, setAvailableCitations] = useState<number>(0);

  const fetchSubscription = async () => {
    if (!user) {
      console.log('No user, setting to free plan');
      setCurrentPlan('free');
      setSubscription(null);
      setLoading(false);
      return;
    }

    try {
      console.log('Fetching subscription for user:', user.id);
      
      // First, get the user's subscription tier from the users table
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('subscription_tier')
        .eq('id', user.id)
        .single();

      if (userError) {
        console.log('User not found in database - waiting for trigger to create profile...');
        
        // If user doesn't exist, wait a bit and try again (trigger might be creating it)
        if (userError.code === 'PGRST116') {
          console.log('User not found - checking if trigger will create it...');
          
          // Try multiple times with shorter delays
          for (let attempt = 1; attempt <= 3; attempt++) {
            console.log(`Attempt ${attempt}: Waiting 1 second for trigger...`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const { data: retryData, error: retryError } = await supabase
              .from('users')
              .select('subscription_tier')
              .eq('id', user.id)
              .single();
              
            if (!retryError) {
              console.log('User profile created by trigger, setting plan to:', retryData?.subscription_tier || 'free');
              setCurrentPlan((retryData?.subscription_tier as PlanType) || 'free');
              setSubscription(null);
              setLoading(false);
              return;
            }
          }
          
          // If still not found after 3 attempts, set to free and continue
          console.log('User profile not created by trigger after 3 attempts - setting to free plan');
          setCurrentPlan('free');
          setSubscription(null);
          setLoading(false);
          return;
        }
        
        console.log('Other error fetching user, setting to free plan');
        setCurrentPlan('free');
        setSubscription(null);
        setLoading(false);
        return;
      }

      // Set the current plan from the user's subscription tier
      const planType = (userData?.subscription_tier as PlanType) || 'free';
      console.log('User subscription tier:', planType);
      setCurrentPlan(planType);

      // If user has a paid plan, also fetch subscription details
      if (planType !== 'free') {
        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('user_subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .eq('status', 'active')
          .single();

        if (subscriptionError && subscriptionError.code !== 'PGRST116') {
          console.error('Error fetching subscription details:', subscriptionError);
        }

        setSubscription(subscriptionData || null);
      } else {
        setSubscription(null);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
      setCurrentPlan('free');
      setSubscription(null);
    } finally {
      setLoading(false);
    }
  };

  const fetchCitations = async () => {
    if (!user) {
      setAvailableCitations(0);
      return;
    }

    try {
      const { data, error } = await supabase.rpc('get_available_citations', {
        user_id: user.id
      });
      
      if (error) {
        console.error('Error fetching available citations:', error);
        setAvailableCitations(0);
      } else {
        setAvailableCitations(data || 0);
      }
    } catch (error) {
      console.error('Error fetching citations:', error);
      setAvailableCitations(0);
    }
  };

  const refreshCitations = async () => {
    await fetchCitations();
  };

  const refreshSubscription = async () => {
    setLoading(true);
    await fetchSubscription();
    await fetchCitations();
  };

  useEffect(() => {
    fetchSubscription();
    fetchCitations();
  }, [user]);

  return (
    <PlanContext.Provider value={{ 
      currentPlan, 
      subscription, 
      loading, 
      refreshSubscription,
      availableCitations,
      refreshCitations
    }}>
      {children}
    </PlanContext.Provider>
  );
};

export const usePlan = () => {
  const ctx = useContext(PlanContext);
  if (!ctx) throw new Error('usePlan must be used within a PlanProvider');
  return ctx;
}; 