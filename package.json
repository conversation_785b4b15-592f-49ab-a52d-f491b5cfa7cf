{"name": "amplify", "version": "1.0.0", "description": "", "main": "index.js", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "amplify:sandbox": "npx amplify sandbox"}, "dependencies": {"@aws-amplify/backend-data": "^1.6.1", "@aws-amplify/backend-function": "^1.14.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@stripe/stripe-js": "^7.6.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.51.0", "aws-amplify": "^6.15.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^2.29.3", "embla-carousel-react": "8.5.1", "file-saver": "^2.0.5", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "^15.2.4", "next-themes": "^0.4.4", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "stripe": "^18.3.0", "tailwind": "^4.0.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@aws-amplify/backend": "^1.16.1", "@aws-amplify/backend-cli": "^1.8.0", "@types/file-saver": "^2.0.7", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "aws-cdk-lib": "^2.204.0", "constructs": "^10.4.2", "esbuild": "^0.25.9", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tsx": "^4.20.5", "typescript": "^5.9.2"}}