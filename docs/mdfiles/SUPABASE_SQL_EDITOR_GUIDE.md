# Supabase SQL Editor 执行指南

## 📋 概述

本指南将帮助您在Supabase SQL Editor中安全、高效地执行数据库优化脚本，解决45秒认证超时问题。

## 🚀 执行顺序

### 第一步：基础性能优化
```sql
-- 在Supabase SQL Editor中执行
-- 文件：database_performance_optimization.sql
```

### 第二步：查询特定优化
```sql
-- 在Supabase SQL Editor中执行
-- 文件：query_specific_optimizations.sql
```

### 第三步：系统参数优化（分步执行）
```sql
-- 在Supabase SQL Editor中执行
-- 文件：alter_system_commands.sql
```

### 第四步：内省查询修复
```sql
-- 在Supabase SQL Editor中执行
-- 文件：introspection_query_fix.sql
```

## ⚠️ 重要注意事项

### Supabase限制
1. **ALTER SYSTEM命令** - Supabase可能不支持某些系统级参数修改
2. **权限限制** - 某些操作可能需要超级用户权限
3. **配置持久性** - 会话级设置不会持久化

### 安全建议
1. **备份数据** - 执行前确保有数据备份
2. **分步执行** - 不要一次性执行所有脚本
3. **监控性能** - 执行后观察系统性能变化

## 🔧 执行步骤详解

### 步骤1：基础性能优化
```sql
-- 复制并粘贴 database_performance_optimization.sql 的内容
-- 点击 "Run" 按钮执行
```

### 步骤2：查询特定优化
```sql
-- 复制并粘贴 query_specific_optimizations.sql 的内容
-- 点击 "Run" 按钮执行
```

### 步骤3：系统参数优化
```sql
-- 由于Supabase限制，跳过ALTER SYSTEM命令
-- 直接执行会话级设置：

SET log_statement_stats = off;
SET log_parser_stats = off;
SET log_planner_stats = off;
SET log_executor_stats = off;
SET track_activities = on;
SET track_counts = on;
SET track_io_timing = off;
SET track_functions = none;
```

### 步骤4：内省查询修复
```sql
-- 复制并粘贴 introspection_query_fix.sql 的内容
-- 跳过ALTER SYSTEM部分，只执行其他优化
```

## 📊 验证执行结果

### 检查索引创建
```sql
-- 验证新索引是否创建成功
SELECT schemaname, tablename, indexname 
FROM pg_indexes 
WHERE schemaname = 'public' 
AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;
```

### 检查视图创建
```sql
-- 验证性能监控视图是否创建成功
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('index_usage_stats', 'slow_queries_monitor', 'system_catalog_cache');
```

### 检查函数创建
```sql
-- 验证优化函数是否创建成功
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%optimized%';
```

## 🎯 针对45秒超时的特殊优化

### 创建系统目录查询缓存
```sql
-- 这个表专门用于缓存系统目录查询结果
CREATE TABLE IF NOT EXISTS "public"."system_catalog_cache" (
    "cache_key" "text" PRIMARY KEY,
    "cached_data" "jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "expires_at" timestamp with time zone DEFAULT ("now"() + '1 hour'::interval)
);

-- 添加索引
CREATE INDEX "idx_system_catalog_cache_expires" ON "public"."system_catalog_cache" 
USING "btree" ("expires_at");
```

### 创建缓存清理函数
```sql
-- 自动清理过期缓存
CREATE OR REPLACE FUNCTION "public"."cleanup_system_catalog_cache"()
RETURNS "void"
LANGUAGE "plpgsql"
AS $$
BEGIN
    DELETE FROM "public"."system_catalog_cache" 
    WHERE "expires_at" < "now"();
END;
$$;
```

## 🔍 性能监控

### 查看慢查询
```sql
-- 查看最慢的查询（需要启用pg_stat_statements扩展）
SELECT query, calls, total_exec_time, mean_exec_time, rows
FROM pg_stat_statements
WHERE mean_exec_time > 1000
ORDER BY mean_exec_time DESC
LIMIT 10;
```

### 查看索引使用情况
```sql
-- 查看索引使用统计
SELECT * FROM "public"."index_usage_stats"
ORDER BY idx_scan DESC;
```

## 🚨 故障排除

### 如果执行失败
1. **检查权限** - 确保有足够的数据库权限
2. **查看错误** - 仔细阅读错误信息
3. **分步执行** - 将大脚本分解为小部分
4. **跳过问题** - 暂时跳过有问题的部分

### 常见错误解决
```sql
-- 如果表已存在错误
DROP TABLE IF EXISTS table_name;

-- 如果索引已存在错误
DROP INDEX IF EXISTS index_name;

-- 如果函数已存在错误
DROP FUNCTION IF EXISTS function_name;
```

## 📈 预期效果

执行完成后，您应该看到：
1. **认证速度提升** - 首次认证时间从45秒减少到2-5秒
2. **查询性能改善** - 数据库查询响应时间显著降低
3. **系统稳定性** - 减少超时和错误

## 🔄 回滚计划

如果需要回滚更改：
```sql
-- 删除创建的索引
DROP INDEX IF EXISTS "idx_system_catalog_cache_expires";
-- 删除创建的表
DROP TABLE IF EXISTS "public"."system_catalog_cache";
-- 删除创建的函数
DROP FUNCTION IF EXISTS "public"."cleanup_system_catalog_cache"();
```

## 📞 支持

如果遇到问题：
1. 检查Supabase日志
2. 查看数据库性能指标
3. 联系技术支持

---

**注意**: 在Supabase环境中，某些系统级优化可能受到限制。建议重点关注应用层面的优化和索引创建。
