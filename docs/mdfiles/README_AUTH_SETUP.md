# CiteAI Authentication Setup Guide

This guide will help you set up Google and Microsoft authentication for your CiteAI application using Supabase.

## Prerequisites

1. A Supabase project (create one at https://supabase.com)
2. Google Cloud Console access (for Google OAuth)
3. Microsoft Azure Portal access (for Microsoft OAuth)

## Step 1: Configure Environment Variables

1. Copy the `.env.local` file and update it with your Supabase credentials:

```bash
# Get these from your Supabase project dashboard
NEXT_PUBLIC_SUPABASE_URL= https://sfpvfogiecwnborsdijj.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY= eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNmcHZmb2dpZWN3bmJvcnNkaWpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTUxODMsImV4cCI6MjA2NzkzMTE4M30.3CciuX7cK7Q3e9aYWeU5jC1ZOrVnMN_qL7hmRm6SPHM
```

## Step 2: Set up Google OAuth

### In Google Cloud Console:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Choose "Web application"
6. Add authorized redirect URIs:
   - `https://your-project-id.supabase.co/auth/v1/callback`
   - `http://localhost:3000/auth/callback` (for development)
7. Copy the Client ID and Client Secret

### In Supabase Dashboard:

1. Go to your Supabase project dashboard
2. Navigate to "Authentication" → "Providers"
3. Enable Google provider
4. Enter your Google Client ID and Client Secret
5. Save the configuration

## Step 3: Set up Microsoft OAuth

### In Microsoft Azure Portal:

1. Go to [Azure Portal](https://portal.azure.com/)
2. Navigate to "Azure Active Directory" → "App registrations"
3. Click "New registration"
4. Fill in the details:
   - Name: "CiteAI"
   - Supported account types: "Accounts in any organizational directory and personal Microsoft accounts"
   - Redirect URI: Web → `https://your-project-id.supabase.co/auth/v1/callback`
5. After creation, note the Application (client) ID
6. Go to "Certificates & secrets" → "New client secret"
7. Copy the client secret value

### In Supabase Dashboard:

1. Go to your Supabase project dashboard
2. Navigate to "Authentication" → "Providers"
3. Enable Azure provider
4. Enter your Microsoft Client ID and Client Secret
5. Save the configuration

## Step 4: Configure Database Schema

Run the database schema from your `database_schema.sql` file in your Supabase SQL editor to set up the necessary tables and policies.

## Step 5: Test the Authentication

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Navigate to `http://localhost:3000/get-started`

3. Test both Google and Microsoft sign-in buttons

## Features Implemented

✅ **Google OAuth Integration**
- Sign in with Google account
- Automatic user profile creation
- Redirect to dashboard after authentication

✅ **Microsoft OAuth Integration**
- Sign in with Microsoft account
- Automatic user profile creation
- Redirect to dashboard after authentication

✅ **Email/Password Authentication**
- Traditional sign up and sign in
- Password confirmation
- Form validation

✅ **User Management**
- Authentication context for app-wide state
- Protected dashboard route
- Sign out functionality
- User profile display

✅ **UI Components**
- Responsive authentication forms
- Loading states
- Error handling
- Beautiful amber-themed design

## File Structure

```
citeai-landing/
├── lib/
│   └── supabase.ts          # Supabase client configuration
├── contexts/
│   └── AuthContext.tsx      # Authentication context provider
├── app/
│   ├── layout.tsx           # Root layout with AuthProvider
│   ├── page.tsx             # Landing page with auth-aware navigation
│   ├── get-started/
│   │   └── page.tsx         # Authentication forms
│   └── dashboard/
│       └── page.tsx         # Protected dashboard page
└── .env.local               # Environment variables
```

## Next Steps

1. **Complete the setup**: Add your actual Supabase credentials to `.env.local`
2. **Test authentication**: Try signing in with Google and Microsoft
3. **Customize the dashboard**: Add your citation generation functionality
4. **Add user profile management**: Allow users to update their information
5. **Implement subscription tiers**: Connect to your database schema for user tiers

## Troubleshooting

### Common Issues:

1. **"Invalid redirect URI"**: Make sure the redirect URI in your OAuth provider matches exactly what's configured in Supabase
2. **"Provider not enabled"**: Ensure Google and Microsoft providers are enabled in your Supabase dashboard
3. **"Environment variables not found"**: Check that your `.env.local` file is in the correct location and has the right variable names

### Debug Tips:

- Check the browser console for error messages
- Verify your Supabase project URL and anon key
- Test with the Supabase dashboard's built-in auth testing
- Use the Network tab to see the OAuth flow

## Security Notes

- Never commit your `.env.local` file to version control
- Use environment variables for all sensitive configuration
- Regularly rotate your OAuth client secrets
- Monitor your Supabase logs for suspicious activity 