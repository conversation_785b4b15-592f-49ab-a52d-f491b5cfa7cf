# Stripe Integration Setup Guide

This guide will walk you through setting up Stripe payments for your CiteAI application.

## Prerequisites

1. A Stripe account (sign up at https://stripe.com)
2. Your CiteAI application running locally
3. Supabase database access

## Step 1: Configure Stripe Products and Prices

1. **Log into your Stripe Dashboard**
2. **Create Products for each plan:**
   - Go to Products → Add Product
   - Create three products:
     - **Free Plan** (no price needed)
     - **Plus Plan** ($15.00/month)
     - **Pro Plan** ($30.00/month)

3. **Set up Recurring Prices:**
   - For Plus and Pro plans, create recurring prices
   - Set billing to "Recurring"
   - Choose "Monthly" billing cycle
   - Set the appropriate price

4. **Copy Price IDs:**
   - Note down the price IDs (they start with `price_`)
   - Update `lib/stripe.ts` with your actual price IDs

## Step 2: Environment Variables

1. **Copy `.env.example` to `.env.local`:**
   ```bash
   cp .env.example .env.local
   ```

2. **Add your Stripe TEST keys (sandbox environment):**
   - Go to Stripe Dashboard → Developers → API Keys
   - **Make sure you're in TEST MODE** (toggle in the top right)
   - Get your test publishable key (starts with `pk_test_`)
   - Get your test secret key (starts with `sk_test_`)
   - Add them to `.env.local`:
   ```
   STRIPE_SECRET_KEY=sk_test_...
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
   ```
   
   **Important:** Always use test keys during development. Never use live keys until you're ready for production!

## Step 3: Database Setup

1. **Run the database schema:**
   - Execute the SQL in `database_schema.sql` in your Supabase SQL editor
   - This creates the `user_subscriptions` table

2. **Verify the table was created:**
   - Check your Supabase dashboard → Table Editor
   - You should see the `user_subscriptions` table

## Step 4: Webhook Configuration

1. **Set up Stripe Webhook (TEST MODE):**
   - Go to Stripe Dashboard → Developers → Webhooks
   - **Make sure you're in TEST MODE** (toggle in the top right)
   - Click "Add endpoint"
   - Set endpoint URL to: `https://your-domain.com/api/stripe/webhook`
   - For local development, use Stripe CLI (see below)

2. **Select events to listen for:**
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

3. **Get webhook secret:**
   - Copy the webhook signing secret
   - Add it to `.env.local`:
   ```
   STRIPE_WEBHOOK_SECRET=whsec_...
   ```

## Step 5: Local Development with Stripe CLI

1. **Install Stripe CLI:**
   ```bash
   # macOS
   brew install stripe/stripe-cli/stripe
   
   # Windows
   # Download from https://github.com/stripe/stripe-cli/releases
   ```

2. **Login to Stripe:**
   ```bash
   stripe login
   ```

3. **Forward webhooks to localhost:**
   ```bash
   stripe listen --forward-to localhost:3000/api/stripe/webhook
   ```

4. **Copy the webhook secret:**
   - The CLI will output a webhook secret starting with `whsec_`
   - Use this for local development in `.env.local`

## Step 6: Update Price IDs

1. **Edit `lib/stripe.ts`:**
   - Replace the placeholder price IDs with your actual Stripe price IDs
   - Update the pricing to match your Stripe products

## Step 7: Test the Integration

1. **Start your development server:**
   ```bash
   npm run dev
   ```

2. **Test subscription flow:**
   - Sign up/login to your app
   - Go to Dashboard → Manage Subscription
   - Try upgrading to a paid plan
   - Use Stripe test card: `4242 4242 4242 4242`

3. **Verify webhook processing:**
   - Check your Stripe Dashboard → Webhooks → Recent deliveries
   - Verify events are being processed successfully

## Step 8: Production Deployment

1. **Switch to Live Mode:**
   - **Only when you're ready for real customers!**
   - Toggle to "Live mode" in Stripe Dashboard
   - Update environment variables with live keys (starts with `sk_live_` and `pk_live_`)
   - Update webhook endpoint to your production domain

2. **Test with real cards:**
   - Use Stripe's test mode extensively first
   - Only switch to live mode when thoroughly tested
   - Consider using Stripe's "test mode" for initial customer onboarding

## Troubleshooting

### Common Issues:

1. **Webhook signature verification failed:**
   - Check that `STRIPE_WEBHOOK_SECRET` is correct
   - Ensure webhook URL is accessible

2. **Price ID not found:**
   - Verify price IDs in `lib/stripe.ts` match your Stripe dashboard
   - Check that prices are active in Stripe

3. **Database errors:**
   - Ensure `user_subscriptions` table exists
   - Check RLS policies are set up correctly

4. **Checkout session creation fails:**
   - Verify Stripe keys are correct
   - Check that products/prices exist in Stripe

### Testing Cards (Test Mode Only):

- **Success:** `4242 4242 4242 4242`
- **Decline:** `4000 0000 0000 0002`
- **Requires authentication:** `4000 0025 0000 3155`
- **Insufficient funds:** `4000 0000 0000 9995`
- **Expired card:** `4000 0000 0000 0069`
- **Incorrect CVC:** `4000 0000 0000 0127`

**Note:** These cards only work in test mode. They will be declined in live mode.

## Security Notes

1. **Never commit `.env.local` to version control**
2. **Use environment variables for all sensitive data**
3. **Always verify webhook signatures**
4. **Test thoroughly before going live**
5. **Always use test mode during development**
6. **Never use live keys in development environment**

## Next Steps

1. **Customize the subscription plans** to match your business model
2. **Add usage tracking** for metered billing if needed
3. **Implement customer portal** for subscription management
4. **Add analytics** to track conversion rates
5. **Set up email notifications** for subscription events 