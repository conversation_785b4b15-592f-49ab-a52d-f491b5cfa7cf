# CiteAI Database Schema & Data Models

This document describes the comprehensive data model for the CiteAI citation generation system, including the database schema, Python data models, and setup instructions.

## Overview

CiteAI is an AI-powered citation generator that helps students and researchers automatically generate accurate citations from their essays and research papers. The system uses multiple APIs (Perplexity, Semantic Scholar) and includes caching to reduce API costs.

## Database Schema

### Core Tables

#### 1. Users (`public.users`)
Extends Supabase's `auth.users` table with CiteAI-specific fields:
- **Subscription management**: Free (6 queries/day), Plus (20 queries/day), Pro (unlimited)
- **Usage tracking**: Daily Perplexity query counts with automatic daily resets
- **Excerpt sizing**: 150 words (Free) vs 500 words (Plus/Pro)
- **Profile data**: Name, avatar, subscription status

#### 2. Documents (`public.documents`)
Stores user-uploaded essays and papers:
- **Content**: Full text of the document
- **Processing status**: Tracks citation generation progress
- **Metadata**: File info, word count, topic

#### 3. Citations (`public.citations`)
Individual citations generated from documents:
- **Source data**: URL, title, author, date, venue, DOI
- **CSL data**: Citation Style Language format for bibliography generation
- **Context**: Sentence index and surrounding text for traceability

#### 4. Citation Formats (`public.citation_formats`)
Generated bibliographies in different formats:
- **Formats**: APA, MLA, Chicago, Harvard
- **Output**: HTML and PDF versions
- **Citation count**: Number of citations in the bibliography

#### 5. API Cache (`public.api_cache`)
Reduces API calls by caching responses:
- **Cache types**: Perplexity, Semantic Scholar, web scraping
- **Expiration**: 30-day TTL with hit counting
- **Request/response**: Full API interaction data

#### 6. API Usage (`public.api_usage`)
Tracks API costs and usage:
- **Cost tracking**: Per-user API spending
- **Monthly aggregation**: Usage by month
- **API types**: Separate tracking for different services

#### 7. User Sessions (`public.user_sessions`)
Analytics and usage tracking:
- **Session duration**: Start/end times
- **Activity metrics**: Documents processed, citations generated
- **API calls**: Total calls made during session

#### 8. Subscription Plans (`public.subscription_plans`)
Defines available subscription tiers:
- **Free**: $0/month, 6 Perplexity queries/day, 150-word excerpts, basic features
- **Plus**: $15/month, 20 Perplexity queries/day, 500-word excerpts, premium features
- **Pro**: $30/month, unlimited Perplexity queries/day, 500-word excerpts, all features

### Key Features

#### Row Level Security (RLS)
All tables have RLS policies ensuring users can only access their own data:
- Users can only view/update their own documents, citations, and usage
- API cache is shared among authenticated users for efficiency
- Admin functions for subscription management

#### Automatic Triggers
- **Updated timestamps**: Automatic `updated_at` field updates
- **Citation counting**: Automatic increment of user citation counts and daily Perplexity queries
- **Daily resets**: Automatic reset of daily Perplexity query limits

#### Database Functions
- `can_generate_citations(user_uuid)`: Check subscription limits (6/20/unlimited queries)
- `increment_citation_count(user_uuid)`: Update usage counters
- `reset_daily_perplexity_queries()`: Reset daily Perplexity query limits
- `clean_expired_cache()`: Remove expired cache entries

## Python Data Models

The `data_models.py` file provides Python classes that map to the database schema:

### Core Models

```python
from data_models import User, Document, Citation, ApiCache, CitationFormat

# Create a new user
user = User(
    id="user-uuid",
    email="<EMAIL>",
    subscription_tier=SubscriptionTier.PLUS
)

# Create a document
document = Document(
    id="doc-uuid",
    user_id=user.id,
    title="My Research Paper",
    content="Full text content...",
    topic="Computer Science"
)

# Generate a citation
citation = Citation(
    id="citation-uuid",
    document_id=document.id,
    user_id=user.id,
    source_url="https://example.com",
    source_title="Research Paper Title",
    csl_data={"title": "Paper Title", "author": [...]}
)
```

### Enum Types

```python
# Subscription tiers
SubscriptionTier.FREE    # 6 queries/day, 150-word excerpts
SubscriptionTier.PLUS    # 20 queries/day, 500-word excerpts
SubscriptionTier.PRO     # Unlimited queries, 500-word excerpts

# Document status
DocumentStatus.PROCESSING
DocumentStatus.COMPLETED
DocumentStatus.FAILED

# Citation formats
CitationFormatType.APA
CitationFormatType.MLA
CitationFormatType.CHICAGO
CitationFormatType.HARVARD

# API types
ApiType.PERPLEXITY
ApiType.SEMANTIC_SCHOLAR
```

## Database Setup Instructions

### 1. Supabase Setup

1. **Create Supabase Project**:
   ```bash
   # Install Supabase CLI
   npm install -g supabase
   
   # Login to Supabase
   supabase login
   
   # Create new project
   supabase projects create citeai
   ```

2. **Run Database Schema**:
   ```bash
   # Apply the schema
   supabase db push
   
   # Or run the SQL directly in Supabase dashboard
   # Copy and paste database_schema.sql
   ```

### 2. Environment Configuration

Create a `.env` file with your Supabase credentials:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# API Keys
PERPLEXITY_API_KEY=your-perplexity-key
SEMANTIC_SCHOLAR_API_KEY=your-semantic-scholar-key

# Storage (for PDFs)
SUPABASE_STORAGE_BUCKET=citeai-documents
```

### 3. Python Integration

Install required packages:

```bash
pip install supabase python-dotenv
```

Create a database client:

```python
import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

supabase: Client = create_client(
    os.environ.get("SUPABASE_URL"),
    os.environ.get("SUPABASE_ANON_KEY")
)
```

### 4. Scheduled Jobs

Set up cron jobs for maintenance:

```sql
-- Reset daily Perplexity queries (daily at midnight)
SELECT cron.schedule('reset-daily-perplexity-queries', '0 0 * * *', 
  'SELECT reset_daily_perplexity_queries();');

-- Clean expired cache (weekly on Sunday)
SELECT cron.schedule('clean-expired-cache', '0 0 * * 0', 
  'SELECT clean_expired_cache();');
```

## Usage Examples

### Creating a New Document

```python
from data_models import Document, DocumentStatus
import uuid

def create_document(user_id: str, title: str, content: str) -> Document:
    document = Document(
        id=str(uuid.uuid4()),
        user_id=user_id,
        title=title,
        content=content,
        word_count=len(content.split())
    )
    
    # Insert into database
    result = supabase.table('documents').insert(document.to_dict()).execute()
    return Document.from_dict(result.data[0])
```

### Generating Citations

```python
from data_models import Citation, ApiCache, CacheType
import hashlib
import json

def generate_citation(document_id: str, user_id: str, context_text: str) -> Citation:
    # Check if user can generate citations
    user = get_user(user_id)
    if not user.can_generate_citations():
        raise Exception("Citation limit reached")
    
    # Check cache first
    cache_key = ApiCache.create_cache_key(
        CacheType.PERPLEXITY, 
        {"context": context_text}
    )
    
    cached = supabase.table('api_cache').select('*').eq('cache_key', cache_key).execute()
    
    if cached.data:
        # Use cached response
        cache_entry = ApiCache.from_dict(cached.data[0])
        cache_entry.increment_hit_count()
        supabase.table('api_cache').update(cache_entry.to_dict()).eq('id', cache_entry.id).execute()
    else:
        # Make API call and cache result
        response = call_perplexity_api(context_text)
        cache_entry = ApiCache(
            id=str(uuid.uuid4()),
            cache_key=cache_key,
            cache_type=CacheType.PERPLEXITY,
            request_data={"context": context_text},
            response_data=response
        )
        supabase.table('api_cache').insert(cache_entry.to_dict()).execute()
    
    # Create citation
    citation = Citation(
        id=str(uuid.uuid4()),
        document_id=document_id,
        user_id=user_id,
        cache_id=cache_entry.id,
        source_url=response.get('url'),
        source_title=response.get('title'),
        is_research_paper=response.get('is_paper', False),
        context_text=context_text,
        csl_data=build_csl_json(response)
    )
    
    # Insert citation and update user count
    supabase.table('citations').insert(citation.to_dict()).execute()
    user.increment_citation_count()
    supabase.table('users').update(user.to_dict()).eq('id', user.id).execute()
    
    return citation
```

### Generating Bibliography

```python
from data_models import CitationFormat, CitationFormatType

def generate_bibliography(document_id: str, user_id: str, format_type: CitationFormatType) -> CitationFormat:
    # Get all citations for the document
    citations = supabase.table('citations').select('*').eq('document_id', document_id).execute()
    
    # Convert to CSL format
    csl_items = [citation['csl_data'] for citation in citations.data]
    
    # Generate bibliography using filegen.py
    bibliography_html = generate_bibliography_html(csl_items, format_type.value)
    
    # Store in database
    citation_format = CitationFormat(
        id=str(uuid.uuid4()),
        document_id=document_id,
        user_id=user_id,
        format_type=format_type,
        bibliography_html=bibliography_html,
        citation_count=len(csl_items)
    )
    
    result = supabase.table('citation_formats').insert(citation_format.to_dict()).execute()
    return CitationFormat.from_dict(result.data[0])
```

## Performance Considerations

### Indexes
The schema includes strategic indexes for common queries:
- User email and subscription tier lookups
- Document status and user filtering
- Citation document and user relationships
- API cache key and expiration queries
- Usage tracking by user and month

### Caching Strategy
- **API responses**: 30-day TTL with hit counting
- **User data**: Session-based caching
- **Bibliography**: Generated on-demand, cached by format

### Rate Limiting
- **Free tier**: 5 citations/month
- **Plus tier**: Unlimited with fair use
- **API costs**: Tracked per user and month

## Security Features

### Row Level Security
- Users can only access their own data
- Admin functions for subscription management
- Shared cache for efficiency while maintaining privacy

### Data Validation
- Enum constraints for status fields
- Check constraints for subscription tiers
- JSONB validation for complex data

### Audit Trail
- Automatic timestamps on all records
- Session tracking for analytics
- API usage logging for cost management

## Monitoring & Analytics

### Key Metrics
- Citations generated per user
- API usage and costs
- Cache hit rates
- Document processing times
- Subscription conversion rates

### Health Checks
- Database connection status
- API endpoint availability
- Cache expiration monitoring
- Monthly usage resets

## Future Enhancements

### Planned Features
- **Multi-language support**: Additional citation formats
- **Batch processing**: Multiple documents at once
- **Advanced caching**: Redis integration for better performance
- **Analytics dashboard**: User behavior insights
- **API rate limiting**: Per-user API call limits
- **Export formats**: Word, LaTeX, BibTeX support

### Scalability Considerations
- **Horizontal scaling**: Read replicas for analytics
- **Caching layers**: Redis for session and API data
- **CDN integration**: Fast PDF delivery
- **Queue system**: Background citation processing

This data model provides a solid foundation for the CiteAI system, supporting user management, document processing, citation generation, and subscription management while maintaining security and performance. 