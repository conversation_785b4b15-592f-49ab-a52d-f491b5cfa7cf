# 🚀 Supabase SQL Editor 快速执行指南

## 目标
解决45秒认证超时问题，将首次认证时间减少到2-5秒。

## 📋 执行步骤

### 1. 打开Supabase SQL Editor
- 登录Supabase Dashboard
- 进入您的项目
- 点击左侧菜单的 "SQL Editor"

### 2. 执行优化脚本
复制以下完整脚本到SQL Editor中：

```sql
-- 复制 docs/sql_queries/supabase_optimized_script.sql 的完整内容
-- 然后点击 "Run" 按钮
```

### 3. 验证执行结果
执行完成后，您应该看到：
- ✅ 成功创建了多个索引
- ✅ 成功创建了缓存表
- ✅ 成功创建了性能监控视图
- ✅ 显示 "Database optimization completed successfully!"

## 🔍 验证性能改善

### 测试认证性能
```sql
-- 在SQL Editor中执行此查询来测试性能
SELECT * FROM "public"."test_auth_performance"();
```

### 查看索引使用情况
```sql
-- 查看索引使用统计
SELECT * FROM "public"."index_usage_stats"
ORDER BY idx_scan DESC;
```

## ⚠️ 注意事项

1. **一次性执行** - 将整个脚本复制粘贴后一次性执行
2. **无需分步** - 脚本已经过优化，适合Supabase环境
3. **自动清理** - 脚本包含错误处理，不会重复创建对象

## 🎯 预期效果

执行完成后：
- **认证速度提升** - 从45秒减少到2-5秒
- **查询性能改善** - 数据库响应时间显著降低
- **系统稳定性** - 减少超时和错误

## 🔄 如果需要回滚

```sql
-- 删除创建的索引
DROP INDEX IF EXISTS "idx_users_auth_optimized";
DROP INDEX IF EXISTS "idx_documents_user_optimized";
DROP INDEX IF EXISTS "idx_citations_document_optimized";
DROP INDEX IF EXISTS "idx_users_subscription_covering";

-- 删除创建的表
DROP TABLE IF EXISTS "public"."system_catalog_cache";

-- 删除创建的视图
DROP VIEW IF EXISTS "public"."index_usage_stats";
DROP VIEW IF EXISTS "public"."slow_queries_monitor";

-- 删除创建的函数
DROP FUNCTION IF EXISTS "public"."cleanup_system_catalog_cache"();
DROP FUNCTION IF EXISTS "public"."get_cached_system_data"("text");
DROP FUNCTION IF EXISTS "public"."set_cached_system_data"("text", "jsonb");
DROP FUNCTION IF EXISTS "public"."test_auth_performance"();
```

## 📞 支持

如果遇到问题：
1. 检查错误信息
2. 查看Supabase日志
3. 联系技术支持

---

**执行时间**: 约2-3分钟  
**风险等级**: 低（只创建索引和表，不修改现有数据）  
**回滚难度**: 简单（提供完整回滚脚本）
