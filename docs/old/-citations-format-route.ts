import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import path from 'path';

export async function POST(req: NextRequest) {
  try {
    const { citations, citation_format, html } = await req.json();
    
    if (!citations || !Array.isArray(citations)) {
      return NextResponse.json({ error: 'Invalid citations data' }, { status: 400 });
    }

    // Extract CSL data from stored citations
    const cslItems = citations.map((citation: any) => citation.csl_data);

    // Prepare the Python script path
    const scriptPath = '../main.py';
    
    // Call Python script to format the citations
    const pyArgs = ['--output_mode', html ? 'html' : 'text'];
    const pyProcess = spawn('python3', [scriptPath, ...pyArgs], {
      stdio: ['pipe', 'pipe', 'pipe'],
    });

    // Send the CSL items to Python for formatting
    const inputData = JSON.stringify({
      csl_items: cslItems,
      citation_format: citation_format || 'mla',
      format_only: true // Flag to indicate we're just formatting, not generating
    });

    pyProcess.stdin.write(inputData);
    pyProcess.stdin.end();

    let stdout = '';
    let stderr = '';
    
    pyProcess.stdout.on('data', (data) => { stdout += data.toString(); });
    pyProcess.stderr.on('data', (data) => { stderr += data.toString(); });

    const exitCode = await new Promise((resolve) => { pyProcess.on('close', resolve); });

    if (exitCode !== 0) {
      console.error('Error formatting citations:', stderr);
      return NextResponse.json({ error: 'Failed to format citations' }, { status: 500 });
    }

    if (html) {
      return NextResponse.json({ citation_html: stdout });
    } else {
      return NextResponse.json({ citation: stdout });
    }

  } catch (error) {
    console.error('Error in citation formatting:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
