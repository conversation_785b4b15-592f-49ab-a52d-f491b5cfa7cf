import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function POST(req: NextRequest) {
  try {
    const { documentId, citationFormat = 'mla' } = await req.json();
    
    if (!documentId) {
      return NextResponse.json({ error: 'Document ID is required' }, { status: 400 });
    }

    // Fetch citations for the document
    const { data: citations, error: fetchError } = await supabase
      .from('citations')
      .select('csl_data')
      .eq('document_id', documentId);

    if (fetchError) {
      console.error('Error fetching citations:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch citations' }, { status: 500 });
    }

    if (!citations || citations.length === 0) {
      return NextResponse.json({ error: 'No citations found for this document' }, { status: 404 });
    }

    // Extract CSL data from citations
    const cslItems = citations.map((citation: any) => citation.csl_data);

    // Create temporary files
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const tempJsonPath = path.join('/tmp', `citations-${documentId}-${timestamp}.json`);
    const outputPdfPath = path.join('/tmp', `citations-${documentId}-${timestamp}.pdf`);

    // Write CSL data to temporary JSON file
    fs.writeFileSync(tempJsonPath, JSON.stringify(cslItems, null, 2));

    // Prepare the Python script path (filegen.py)
    const scriptPath = path.join(process.cwd(), '..', 'filegen.py');
    
    // Call Python script to generate PDF
    const pyProcess = spawn('python3', [scriptPath, tempJsonPath, citationFormat, outputPdfPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
    });

    let stdout = '';
    let stderr = '';
    
    pyProcess.stdout.on('data', (data) => { stdout += data.toString(); });
    pyProcess.stderr.on('data', (data) => { stderr += data.toString(); });

    const exitCode = await new Promise((resolve) => { pyProcess.on('close', resolve); });

    // Clean up temporary JSON file
    if (fs.existsSync(tempJsonPath)) {
      fs.unlinkSync(tempJsonPath);
    }

    if (exitCode !== 0) {
      console.error('Error generating PDF:', stderr);
      return NextResponse.json({ error: 'Failed to generate PDF' }, { status: 500 });
    }

    // Check if PDF was generated
    if (!fs.existsSync(outputPdfPath)) {
      return NextResponse.json({ error: 'PDF file was not generated' }, { status: 500 });
    }

    // Read the generated PDF file
    const pdfBuffer = fs.readFileSync(outputPdfPath);
    
    // Clean up the temporary PDF file
    fs.unlinkSync(outputPdfPath);

    // Return the PDF as a downloadable file
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="citations-${citationFormat.toUpperCase()}.pdf"`,
        'Content-Length': pdfBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Error in PDF export:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
