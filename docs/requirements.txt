# CiteAI Project - Complete Python Dependencies
# This file contains all Python dependencies used across the project
# Most of these are provided via AWS Lambda Layer: citeai-python-dependency

# ===== CORE AWS DEPENDENCIES =====
boto3>=1.40.25
botocore>=1.40.25
s3transfer>=0.13.1

# ===== HTTP REQUESTS AND WEB SCRAPING =====
requests>=2.32.5
urllib3>=2.5.0
certifi>=2025.8.3
charset-normalizer>=3.4.3
idna>=3.10

# ===== HTML PARSING =====
beautifulsoup4>=4.13.5
soupsieve>=2.8
lxml>=6.0.1

# ===== CITATION PROCESSING =====
citeproc-py>=0.9.0
citeproc-py-styles>=0.1.5

# ===== PDF GENERATION =====
weasyprint>=66.0
pillow>=11.3.0
fonttools>=4.59.2
cffi>=1.17.1
pycparser>=2.22
tinycss2>=1.4.0
cssselect2>=0.8.0
tinyhtml5>=2.0.0
webencodings>=0.5.1
pyphen>=0.17.2
pydyf>=0.11.0
brotli>=1.1.0
zopfli>=0.2.3.post1

# ===== NATURAL LANGUAGE PROCESSING =====
nltk>=3.9.1
regex>=2025.9.1
tqdm>=4.67.1
joblib>=1.5.2

# ===== DATE AND TIME UTILITIES =====
python-dateutil>=2.9.0.post0

# ===== UTILITY LIBRARIES =====
six>=1.17.0
typing-extensions>=4.15.0
click>=8.2.1
jmespath>=1.0.1

# ===== MISSING FROM LAYER =====
# These dependencies are used in the project but not found in the current layer:
# (None currently - all required dependencies are in the layer)

# ===== DEVELOPMENT AND TESTING =====
# Add any development dependencies here if needed

# ===== NOTES =====
# 1. Most dependencies are provided via AWS Lambda Layer: citeai-python-dependency
# 2. Individual Lambda functions should NOT have their own requirements.txt files
# 3. If new dependencies are needed, add them to the layer and update this file
# 4. Version numbers reflect what's currently in the layer (as of 2025-09-15)
# 5. Some dependencies like reportlab were intentionally removed to reduce size

# ===== LAYER CONTENTS VERIFICATION =====
# The following packages are confirmed to be in the python-dependency_layer:
# - All AWS SDK components (boto3, botocore, s3transfer)
# - All web scraping tools (requests, beautifulsoup4, lxml)
# - All citation processing tools (citeproc-py, citeproc-py-styles)
# - All PDF generation tools (weasyprint and dependencies)
# - All NLP tools (nltk and dependencies)
# - All utility libraries and their dependencies
