# CiteAI Development Guide

This document provides comprehensive information for developers working on the CiteAI project.

## 🏗️ Architecture Overview

CiteAI is built using a modern serverless architecture with AWS Amplify Gen 2, providing scalability, reliability, and cost-effectiveness.

### System Components

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Next.js App   │───▶│  AWS API Gateway │───▶│ Lambda Functions│
│   (Frontend)    │    │                  │    │   (Backend)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                                               │
         ▼                                               ▼
┌─────────────────┐                            ┌─────────────────┐
│   Supabase      │                            │   AWS S3        │
│  (Database)     │                            │ (File Storage)  │
└─────────────────┘                            └─────────────────┘
```

### Key Technologies

- **Frontend**: Next.js 15 with TypeScript and Tailwind CSS
- **Backend**: AWS Lambda functions with Python 3.12
- **Database**: Supabase (PostgreSQL) with Row Level Security
- **Storage**: AWS S3 for PDF files and generated documents
- **Authentication**: Supabase Auth with OAuth providers
- **Payments**: Stripe for subscriptions and microtransactions
- **Infrastructure**: AWS Amplify Gen 2 for deployment and management

## 🚀 Development Setup

### Prerequisites

1. **Node.js 18+** - JavaScript runtime
2. **AWS Account** - For Amplify deployment
3. **Supabase Account** - Database and authentication
4. **Stripe Account** - Payment processing

### Environment Configuration

Create `.env.local` in the project root:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# API Keys (set in Lambda environment variables)
PERPLEXITY_API_KEY=your_perplexity_api_key
SEMANTIC_SCHOLAR_API_KEY=your_semantic_scholar_key
```

### Local Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Deploy to AWS Amplify sandbox
npx amplify sandbox
```

## 📁 Project Structure

### Frontend (`src/`)

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API route handlers
│   ├── dashboard/         # User dashboard
│   ├── document/[id]/     # Document editor
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   └── [feature]/        # Feature-specific components
├── contexts/             # React Context providers
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
└── styles/               # Global styles
```

### Backend (`amplify/functions/`)

```
amplify/functions/
├── call-function/         # Main citation processing
│   ├── index.py          # Lambda handler
│   ├── helpers/          # Helper modules
│   └── resource.ts       # AWS CDK configuration
└── pdfGenerator/         # PDF generation service
    ├── index.py          # Lambda handler
    ├── helpers/          # PDF processing utilities
    └── resource.ts       # AWS CDK configuration
```

## 🔧 Development Workflow

### Adding New Features

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Develop Locally**
   - Use `npm run dev` for frontend development
   - Test Lambda functions with `npx amplify sandbox`

3. **Testing**
   - Write unit tests for new components
   - Test API endpoints with proper error handling
   - Verify database operations and RLS policies

4. **Code Review**
   - Ensure proper TypeScript types
   - Follow existing code patterns
   - Add comprehensive comments

### Database Changes

1. **Create Migration**
   ```sql
   -- supabase/migrations/YYYYMMDD_description.sql
   ALTER TABLE users ADD COLUMN new_feature_flag BOOLEAN DEFAULT FALSE;
   ```

2. **Update Types**
   - Regenerate TypeScript types if needed
   - Update data models in helper functions

3. **Test Migration**
   - Apply migration to development database
   - Verify RLS policies still work correctly

### Lambda Function Development

1. **Local Testing**
   ```bash
   # Test Lambda function locally
   cd amplify/functions/call-function
   python index.py
   ```

2. **Dependencies**
   - Add new Python packages to `requirements.txt`
   - Update Lambda layer if needed

3. **Deployment**
   ```bash
   # Deploy changes to AWS
   npx amplify sandbox
   ```

## 🧪 Testing Guidelines

### Frontend Testing

- **Component Tests**: Test React components in isolation
- **Integration Tests**: Test API route handlers
- **E2E Tests**: Test complete user workflows

### Backend Testing

- **Unit Tests**: Test individual Lambda functions
- **API Tests**: Test external API integrations
- **Database Tests**: Test database functions and queries

### Testing Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run E2E tests
npm run test:e2e
```

## 🔒 Security Considerations

### Authentication
- All API routes require proper authentication
- Use Supabase RLS for data access control
- Validate user permissions before operations

### Data Protection
- Encrypt sensitive data in transit and at rest
- Use environment variables for secrets
- Implement proper CORS policies

### Rate Limiting
- Implement rate limiting for API endpoints
- Use database functions for quota management
- Monitor for abuse patterns

## 📊 Performance Optimization

### Database
- Use optimized indexes for common queries
- Implement proper caching strategies
- Monitor query performance with pg_stat_statements

### Lambda Functions
- Optimize cold start times
- Use appropriate memory allocation
- Implement connection pooling where needed

### Frontend
- Implement proper loading states
- Use React.memo for expensive components
- Optimize bundle size with code splitting

## 🚀 Deployment

### Staging Environment
```bash
# Deploy to staging
npx amplify sandbox --name staging
```

### Production Deployment
```bash
# Deploy to production
npx amplify deploy --branch main
```

### Environment Variables
- Set production environment variables in AWS Amplify console
- Update Stripe webhook endpoints for production
- Configure proper CORS origins

## 🐛 Debugging

### Common Issues

1. **Lambda Function Errors**
   - Check CloudWatch logs for detailed error messages
   - Verify environment variables are set correctly
   - Ensure Lambda layer dependencies are available

2. **Database Connection Issues**
   - Verify Supabase connection strings
   - Check RLS policies for data access
   - Monitor connection pool usage

3. **Authentication Problems**
   - Verify Supabase Auth configuration
   - Check JWT token expiration
   - Ensure proper session management

### Debugging Tools

- **AWS CloudWatch**: Lambda function logs and metrics
- **Supabase Dashboard**: Database queries and real-time monitoring
- **Browser DevTools**: Frontend debugging and network inspection
- **Stripe Dashboard**: Payment processing and webhook logs

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [AWS Amplify Gen 2 Guide](https://docs.amplify.aws/)
- [Supabase Documentation](https://supabase.com/docs)
- [Stripe API Reference](https://stripe.com/docs/api)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with proper tests
4. Submit a pull request with detailed description
5. Ensure all CI checks pass

For questions or support, please open an issue on GitHub or contact the development team.
