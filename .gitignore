# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*

# Build outputs
.next/
out/
build/
dist/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
test_env/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.coverage
htmlcov/
amplify/.venv/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Vercel
.vercel

# Supabase
.supabase/

# Database
*.db
*.sqlite
*.sqlite3

# API keys and secrets (additional protection)
*secret*
*key*
*token*
*password*
*credential*

# CSV reports (if they contain sensitive data)
csv_reports/

# Output files
output.pdf
*.pdf

# Test files
test_*.json
sample_*.json

# Workspace files
*.code-workspace

# Lock files (optional - some teams prefer to commit these)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml 

# AWS

lambda-layer/
.amplify/
python-dependency_layer/