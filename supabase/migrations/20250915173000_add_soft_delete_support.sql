-- 为文档表添加软删除功能
-- Add Soft Delete Support for Documents
-- 在您的 Supabase SQL Editor 中运行此脚本

-- 1. 为 documents 表添加 deleted_at 字段
ALTER TABLE public.documents 
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;

-- 2. 为软删除查询创建索引
CREATE INDEX IF NOT EXISTS idx_documents_deleted_at 
ON public.documents (deleted_at) 
WHERE deleted_at IS NOT NULL;

-- 3. 为活跃文档创建索引（未删除的）
CREATE INDEX IF NOT EXISTS idx_documents_active 
ON public.documents (user_id, created_at DESC) 
WHERE deleted_at IS NULL;

-- 4. 创建软删除文档的函数
CREATE OR REPLACE FUNCTION soft_delete_document(doc_id UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    UPDATE public.documents 
    SET deleted_at = NOW(), updated_at = NOW()
    WHERE id = doc_id 
    AND user_id = user_uuid 
    AND deleted_at IS NULL;
    
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    RETURN affected_rows > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. 创建恢复文档的函数
CREATE OR REPLACE FUNCTION restore_document(doc_id UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    UPDATE public.documents 
    SET deleted_at = NULL, updated_at = NOW()
    WHERE id = doc_id 
    AND user_id = user_uuid 
    AND deleted_at IS NOT NULL;
    
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    RETURN affected_rows > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. 创建获取文档计数的函数
CREATE OR REPLACE FUNCTION get_document_counts(user_uuid UUID)
RETURNS TABLE(active_count BIGINT, deleted_count BIGINT) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) FILTER (WHERE deleted_at IS NULL) as active_count,
        COUNT(*) FILTER (WHERE deleted_at IS NOT NULL) as deleted_count
    FROM public.documents 
    WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. 创建永久删除旧文档的函数
CREATE OR REPLACE FUNCTION permanently_delete_old_documents(days_old INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除在垃圾箱中超过指定天数的文档
    DELETE FROM public.documents 
    WHERE deleted_at IS NOT NULL 
    AND deleted_at < NOW() - INTERVAL '1 day' * days_old;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. 授予必要的权限
GRANT EXECUTE ON FUNCTION soft_delete_document(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION restore_document(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_document_counts(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION permanently_delete_old_documents(INTEGER) TO service_role;

-- 9. 创建活跃文档的视图（便于使用）
CREATE OR REPLACE VIEW active_documents AS
SELECT * FROM public.documents 
WHERE deleted_at IS NULL;

-- 10. 创建已删除文档的视图（便于使用）
CREATE OR REPLACE VIEW deleted_documents AS
SELECT * FROM public.documents 
WHERE deleted_at IS NOT NULL;

-- 授予视图访问权限
GRANT SELECT ON active_documents TO authenticated;
GRANT SELECT ON deleted_documents TO authenticated;

-- 输出完成信息
DO $$
BEGIN
    RAISE NOTICE '软删除支持添加成功！';
    RAISE NOTICE '已为 documents 表添加 deleted_at 字段';
    RAISE NOTICE '已创建性能索引';
    RAISE NOTICE '已创建辅助函数';
    RAISE NOTICE '已创建便于使用的视图';
END $$;
