

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE OR REPLACE FUNCTION "public"."add_document_citations"("doc_id" "uuid", "user_uuid" "uuid", "citations_data" "jsonb") RETURNS integer
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    citation_record JSONB;
    citation_count INTEGER := 0;
BEGIN
    -- Loop through each citation in the JSONB array
    FOR citation_record IN SELECT * FROM jsonb_array_elements(citations_data)
    LOOP
        INSERT INTO public.citations (
            document_id,
            user_id,
            source_url,
            source_title,
            is_research_paper,
            csl_data
        ) VALUES (
            doc_id,
            user_uuid,
            citation_record->>'URL',
            citation_record->>'title',
            COALESCE((citation_record->>'is_paper')::BOOLEAN, FALSE),
            citation_record
        );
        
        citation_count := citation_count + 1;
    END LOOP;
    
    RETURN citation_count;
END;
$$;


ALTER FUNCTION "public"."add_document_citations"("doc_id" "uuid", "user_uuid" "uuid", "citations_data" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."clean_expired_cache"() RETURNS integer
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.api_cache 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;


ALTER FUNCTION "public"."clean_expired_cache"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."cleanup_old_data"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- 清理30天前的查询日志
    DELETE FROM "public"."query_performance_log" 
    WHERE "created_at" < NOW() - INTERVAL '30 days';
    
    -- 清理过期的API缓存
    DELETE FROM "public"."api_cache" 
    WHERE "expires_at" < NOW();
    
    -- 清理旧的用户会话
    DELETE FROM "public"."user_sessions" 
    WHERE "session_start" < NOW() - INTERVAL '7 days';
END;
$$;


ALTER FUNCTION "public"."cleanup_old_data"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."cleanup_system_catalog_cache"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    DELETE FROM "public"."system_catalog_cache" 
    WHERE "expires_at" < "now"();
    
    -- 记录清理操作
    RAISE NOTICE 'System catalog cache cleaned up at %', "now"();
END;
$$;


ALTER FUNCTION "public"."cleanup_system_catalog_cache"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."clear_document_citations"("doc_id" "uuid") RETURNS integer
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.citations WHERE document_id = doc_id;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;


ALTER FUNCTION "public"."clear_document_citations"("doc_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."consume_citation"("user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    available_citations INTEGER;
BEGIN
    -- Get available citations
    available_citations := get_available_citations(user_id);
    
    -- Check if user has citations available
    IF available_citations > 0 THEN
        -- Increment daily usage
        UPDATE users 
        SET daily_citations_used = daily_citations_used + 1
        WHERE id = user_id;
        RETURN TRUE;
    ELSE
        RETURN FALSE;
    END IF;
END;
$$;


ALTER FUNCTION "public"."consume_citation"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."consume_citation"("user_id" "uuid", "count_to_consume" integer DEFAULT 1) RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    available_citations INTEGER;
    user_tier TEXT;
    user_daily_citations_used INTEGER;
    user_purchased_citations INTEGER;
    daily_limit INTEGER;
    today_date DATE;
BEGIN
    today_date := CURRENT_DATE;
    
    -- Get available citations (this will also handle daily reset)
    available_citations := get_available_citations(user_id);
    
    -- Check if user has enough citations available
    IF available_citations >= count_to_consume THEN
        -- Get current user data
        SELECT subscription_tier, daily_citations_used, purchased_citations
        INTO user_tier, user_daily_citations_used, user_purchased_citations
        FROM public.users 
        WHERE id = user_id;
        
        -- Set daily limit
        IF user_tier = 'pro' THEN
            daily_limit := 999999;
        ELSIF user_tier = 'plus' THEN
            daily_limit := 20;
        ELSE
            daily_limit := 3;
        END IF;
        
        -- Consume citations with priority: daily limit first, then purchased
        IF user_daily_citations_used < daily_limit THEN
            -- Use daily citations first
            UPDATE public.users 
            SET daily_citations_used = daily_citations_used + count_to_consume
            WHERE id = user_id;
        ELSE
            -- Daily limit reached, use purchased citations
            UPDATE public.users 
            SET purchased_citations = GREATEST(0, purchased_citations - count_to_consume)
            WHERE id = user_id;
        END IF;
        
        RETURN TRUE;
    ELSE
        RETURN FALSE;
    END IF;
END;
$$;


ALTER FUNCTION "public"."consume_citation"("user_id" "uuid", "count_to_consume" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."extract_names_from_oauth"("meta_data" "jsonb") RETURNS TABLE("first_name" "text", "last_name" "text")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(
            meta_data->>'first_name',
            meta_data->>'given_name',
            meta_data->>'name',
            SPLIT_PART(COALESCE(meta_data->>'name', meta_data->>'full_name', ''), ' ', 1)
        )::TEXT,
        COALESCE(
            meta_data->>'last_name',
            meta_data->>'family_name',
            CASE 
                WHEN meta_data->>'name' IS NOT NULL AND POSITION(' ' IN meta_data->>'name') > 0 
                THEN SPLIT_PART(meta_data->>'name', ' ', 2)
                WHEN meta_data->>'full_name' IS NOT NULL AND POSITION(' ' IN meta_data->>'full_name') > 0 
                THEN SPLIT_PART(meta_data->>'full_name', ' ', 2)
                ELSE ''
            END
        )::TEXT;
END;
$$;


ALTER FUNCTION "public"."extract_names_from_oauth"("meta_data" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_available_citations"("user_id" "uuid") RETURNS integer
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    user_tier TEXT;
    user_daily_citations_used INTEGER;
    user_purchased_citations INTEGER;
    user_last_reset_date DATE;
    daily_limit INTEGER;
    available_citations INTEGER;
    today_date DATE;
BEGIN
    today_date := CURRENT_DATE;
    
    -- Get user's current plan and usage
    SELECT subscription_tier, daily_citations_used, purchased_citations, last_citation_reset_date
    INTO user_tier, user_daily_citations_used, user_purchased_citations, user_last_reset_date
    FROM public.users 
    WHERE id = user_id;
    
    -- Check if we need to reset daily citations (for both free and plus users)
    IF user_last_reset_date IS NULL OR user_last_reset_date < today_date THEN
        -- Reset daily citations to 0
        UPDATE public.users 
        SET daily_citations_used = 0, 
            last_citation_reset_date = today_date
        WHERE id = user_id;
        
        user_daily_citations_used := 0;
    END IF;
    
    -- Set daily limits based on plan
    IF user_tier = 'pro' THEN
        daily_limit := 999999; -- Effectively unlimited
    ELSIF user_tier = 'plus' THEN
        daily_limit := 20;
    ELSE
        daily_limit := 3; -- Free users get 3 per day
    END IF;
    
    -- Calculate available citations
    -- Base daily limit - daily citations used + purchased citations
    available_citations := GREATEST(0, daily_limit - user_daily_citations_used + user_purchased_citations);
    
    RETURN available_citations;
END;
$$;


ALTER FUNCTION "public"."get_available_citations"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_available_citations_optimized"("user_id" "uuid") RETURNS integer
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    user_tier TEXT;
    user_daily_citations_used INTEGER;
    user_purchased_citations INTEGER;
    user_last_reset_date DATE;
    daily_limit INTEGER;
    available_citations INTEGER;
    today_date DATE;
    needs_reset BOOLEAN := FALSE;
BEGIN
    today_date := CURRENT_DATE;
    
    -- 一次性获取所有需要的用户数据
    SELECT 
        subscription_tier, 
        daily_citations_used, 
        purchased_citations, 
        last_citation_reset_date
    INTO 
        user_tier, 
        user_daily_citations_used, 
        user_purchased_citations, 
        user_last_reset_date
    FROM public.users 
    WHERE id = user_id;
    
    -- 检查是否需要重置（避免不必要的更新）
    IF user_last_reset_date IS NULL OR user_last_reset_date < today_date THEN
        needs_reset := TRUE;
        user_daily_citations_used := 0;
    END IF;
    
    -- 设置每日限制
    IF user_tier = 'pro' THEN
        daily_limit := 999999;
    ELSIF user_tier = 'plus' THEN
        daily_limit := 20;
    ELSE
        daily_limit := 3;
    END IF;
    
    -- 计算可用引用数
    available_citations := GREATEST(0, daily_limit - user_daily_citations_used + user_purchased_citations);
    
    -- 只有在需要重置时才更新数据库
    IF needs_reset THEN
        UPDATE public.users 
        SET 
            daily_citations_used = 0, 
            last_citation_reset_date = today_date,
            updated_at = NOW()
        WHERE id = user_id;
    END IF;
    
    RETURN available_citations;
END;
$$;


ALTER FUNCTION "public"."get_available_citations_optimized"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_cached_system_data"("key" "text") RETURNS "jsonb"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    result "jsonb";
BEGIN
    SELECT "cached_data" INTO result
    FROM "public"."system_catalog_cache"
    WHERE "cache_key" = "key" AND "expires_at" > "now"();
    
    RETURN COALESCE(result, 'null'::jsonb);
END;
$$;


ALTER FUNCTION "public"."get_cached_system_data"("key" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_document_citations"("doc_id" "uuid") RETURNS TABLE("id" "uuid", "source_url" "text", "source_title" "text", "is_research_paper" boolean, "csl_data" "jsonb", "created_at" timestamp with time zone)
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.source_url,
        c.source_title,
        c.is_research_paper,
        c.csl_data,
        c.created_at
    FROM public.citations c
    WHERE c.document_id = doc_id
    ORDER BY c.created_at ASC;
END;
$$;


ALTER FUNCTION "public"."get_document_citations"("doc_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_auth_data"("user_id" "uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    result "jsonb";
    cache_key "text";
    cached_data "jsonb";
BEGIN
    -- 构建缓存键
    cache_key := 'user_auth_' || "user_id"::text;
    
    -- 尝试从缓存获取
    cached_data := "public"."get_cached_system_data"(cache_key);
    
    IF cached_data != 'null'::jsonb THEN
        RETURN cached_data;
    END IF;
    
    -- 从数据库查询
    SELECT jsonb_build_object(
        'id', "id",
        'subscription_tier', "subscription_tier",
        'plan_type', "plan_type",
        'email', "email",
        'created_at', "created_at",
        'updated_at', "updated_at"
    ) INTO result
    FROM "public"."users"
    WHERE "id" = "user_id"
    LIMIT 1;
    
    -- 缓存结果
    IF result IS NOT NULL THEN
        PERFORM "public"."set_cached_system_data"(cache_key, result);
    END IF;
    
    RETURN COALESCE(result, 'null'::jsonb);
END;
$$;


ALTER FUNCTION "public"."get_user_auth_data"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_subscription_data"("user_id" "uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    result "jsonb";
    cache_key "text";
    cached_data "jsonb";
BEGIN
    -- 构建缓存键
    cache_key := 'user_subscription_' || "user_id"::text;
    
    -- 尝试从缓存获取
    cached_data := "public"."get_cached_system_data"(cache_key);
    
    IF cached_data != 'null'::jsonb THEN
        RETURN cached_data;
    END IF;
    
    -- 从数据库查询
    SELECT jsonb_build_object(
        'id', "id",
        'user_id', "user_id",
        'plan_type', "plan_type",
        'status', "status",
        'stripe_subscription_id', "stripe_subscription_id",
        'current_period_start', "current_period_start",
        'current_period_end', "current_period_end"
    ) INTO result
    FROM "public"."user_subscriptions"
    WHERE "user_id" = "user_id"
    AND "status" = 'active'
    ORDER BY "created_at" DESC
    LIMIT 1;
    
    -- 缓存结果
    IF result IS NOT NULL THEN
        PERFORM "public"."set_cached_system_data"(cache_key, result);
    END IF;
    
    RETURN COALESCE(result, 'null'::jsonb);
END;
$$;


ALTER FUNCTION "public"."get_user_subscription_data"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_auth_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
begin
  insert into public.users (id, email, first_name, last_name)
  values (
    new.id, 
    new.email,
    coalesce(new.raw_user_meta_data->>'first_name', ''),
    coalesce(new.raw_user_meta_data->>'last_name', '')
  );
  return new;
end;
$$;


ALTER FUNCTION "public"."handle_new_auth_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    INSERT INTO public.users (
        id,
        email,
        first_name,
        last_name,
        subscription_tier,
        subscription_status,
        created_at,
        updated_at,
        citation_count,
        daily_perplexity_queries,
        daily_perplexity_reset_date,
        excerpt_size_words,
        plan_type
    )
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(
            NEW.raw_user_meta_data->>'first_name',
            SPLIT_PART(COALESCE(NEW.raw_user_meta_data->>'name', NEW.raw_user_meta_data->>'full_name', ''), ' ', 1)
        ),
        COALESCE(
            NEW.raw_user_meta_data->>'last_name',
            CASE 
                WHEN NEW.raw_user_meta_data->>'name' IS NOT NULL AND POSITION(' ' IN NEW.raw_user_meta_data->>'name') > 0 
                THEN TRIM(SUBSTRING(NEW.raw_user_meta_data->>'name' FROM POSITION(' ' IN NEW.raw_user_meta_data->>'name') + 1))
                WHEN NEW.raw_user_meta_data->>'full_name' IS NOT NULL AND POSITION(' ' IN NEW.raw_user_meta_data->>'full_name') > 0 
                THEN TRIM(SUBSTRING(NEW.raw_user_meta_data->>'full_name' FROM POSITION(' ' IN NEW.raw_user_meta_data->>'full_name') + 1))
                ELSE NULL 
            END
        ),
        'free',
        'active',
        NOW(),
        NOW(),
        0,
        0,
        CURRENT_DATE,
        150,
        'free'
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = COALESCE(EXCLUDED.first_name, users.first_name),
        last_name = COALESCE(EXCLUDED.last_name, users.last_name),
        updated_at = NOW();
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error in handle_new_user trigger: %', SQLERRM;
        RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."maintain_database_performance"() RETURNS "jsonb"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    result jsonb;
    cache_cleaned integer;
    stats_updated boolean;
BEGIN
    -- 清理过期缓存
    DELETE FROM "public"."system_catalog_cache" 
    WHERE "expires_at" < "now"();
    
    GET DIAGNOSTICS cache_cleaned = ROW_COUNT;
    
    -- 更新表统计信息
    ANALYZE "public"."users";
    ANALYZE "public"."documents";
    ANALYZE "public"."citations";
    ANALYZE "public"."user_subscriptions";
    ANALYZE "public"."system_catalog_cache";
    
    stats_updated := true;
    
    result := jsonb_build_object(
        'maintenance_time', "now"(),
        'cache_cleaned', cache_cleaned,
        'stats_updated', stats_updated,
        'status', 'completed'
    );
    
    RETURN result;
END;
$$;


ALTER FUNCTION "public"."maintain_database_performance"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."reset_daily_perplexity_queries"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    UPDATE public.users 
    SET daily_perplexity_queries = 0,
        daily_perplexity_reset_date = CURRENT_DATE + INTERVAL '1 day',
        updated_at = NOW()
    WHERE daily_perplexity_reset_date <= CURRENT_DATE;
END;
$$;


ALTER FUNCTION "public"."reset_daily_perplexity_queries"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_cached_system_data"("key" "text", "data" "jsonb") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    INSERT INTO "public"."system_catalog_cache" ("cache_key", "cached_data", "expires_at")
    VALUES ("key", "data", "now"() + '1 hour'::interval)
    ON CONFLICT ("cache_key") 
    DO UPDATE SET 
        "cached_data" = EXCLUDED."cached_data",
        "expires_at" = EXCLUDED."expires_at",
        "created_at" = "now"();
END;
$$;


ALTER FUNCTION "public"."set_cached_system_data"("key" "text", "data" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."test_auth_performance"() RETURNS "jsonb"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    start_time timestamp;
    end_time timestamp;
    result jsonb;
    test_user_id uuid;
BEGIN
    -- 获取一个测试用户ID
    SELECT "id" INTO test_user_id FROM "public"."users" LIMIT 1;
    
    IF test_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'error', 'No users found for testing',
            'status', 'failed'
        );
    END IF;
    
    start_time := clock_timestamp();
    
    -- 测试用户查询性能
    PERFORM "public"."get_user_auth_data"(test_user_id);
    
    end_time := clock_timestamp();
    
    result := jsonb_build_object(
        'test_type', 'authentication_performance',
        'start_time', start_time,
        'end_time', end_time,
        'duration_ms', EXTRACT(EPOCH FROM (end_time - start_time)) * 1000,
        'test_user_id', test_user_id,
        'status', 'completed'
    );
    
    RETURN result;
END;
$$;


ALTER FUNCTION "public"."test_auth_performance"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."api_cache" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "cache_key" "text" NOT NULL,
    "cache_type" "text" NOT NULL,
    "request_data" "jsonb" NOT NULL,
    "response_data" "jsonb" NOT NULL,
    "title" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "expires_at" timestamp with time zone DEFAULT ("now"() + '30 days'::interval),
    CONSTRAINT "api_cache_cache_type_check" CHECK (("cache_type" = ANY (ARRAY['perplexity'::"text", 'semantic_scholar'::"text", 'web_scraping'::"text"])))
);


ALTER TABLE "public"."api_cache" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."api_usage" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "api_type" "text" NOT NULL,
    "request_count" integer DEFAULT 1,
    "cost_usd" numeric(10,4) DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "api_usage_api_type_check" CHECK (("api_type" = ANY (ARRAY['perplexity'::"text", 'semantic_scholar'::"text"])))
);


ALTER TABLE "public"."api_usage" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."citation_formats" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "document_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "format_type" "text" NOT NULL,
    "bibliography_html" "text" NOT NULL,
    "bibliography_pdf_url" "text",
    "citation_count" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "citation_formats_format_type_check" CHECK (("format_type" = ANY (ARRAY['apa'::"text", 'mla'::"text", 'chicago'::"text", 'harvard'::"text"])))
);


ALTER TABLE "public"."citation_formats" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."citations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "document_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "source_url" "text",
    "source_title" "text" NOT NULL,
    "is_research_paper" boolean DEFAULT false,
    "csl_data" "jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."citations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."documents" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "title" "text" NOT NULL,
    "content" "text" NOT NULL,
    "file_name" "text",
    "file_size" integer,
    "word_count" integer,
    "status" "text" DEFAULT 'processing'::"text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "processed_at" timestamp with time zone,
    "flagged_sentences_count" integer DEFAULT 0,
    "citations_generated_count" integer DEFAULT 0,
    "citations_html" "text",
    CONSTRAINT "documents_status_check" CHECK (("status" = ANY (ARRAY['processing'::"text", 'completed'::"text", 'failed'::"text", 'cancelled'::"text"])))
);


ALTER TABLE "public"."documents" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."index_usage_stats" AS
 SELECT "schemaname",
    "relname" AS "tablename",
    "indexrelname" AS "indexname",
    "idx_scan",
    "idx_tup_read",
    "idx_tup_fetch",
        CASE
            WHEN ("idx_scan" = 0) THEN 'UNUSED'::"text"
            WHEN ("idx_scan" < 100) THEN 'LOW_USAGE'::"text"
            WHEN ("idx_scan" < 1000) THEN 'MEDIUM_USAGE'::"text"
            ELSE 'HIGH_USAGE'::"text"
        END AS "usage_level"
   FROM "pg_stat_user_indexes"
  WHERE ("schemaname" = 'public'::"name")
  ORDER BY "idx_scan" DESC;


ALTER VIEW "public"."index_usage_stats" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."query_optimization_suggestions" AS
 SELECT 'documents'::"text" AS "table_name",
    'Consider adding index on (user_id, status, created_at) for status filtering'::"text" AS "suggestion"
  WHERE (EXISTS ( SELECT 1
           FROM "pg_stat_user_tables"
          WHERE (("pg_stat_user_tables"."schemaname" = 'public'::"name") AND ("pg_stat_user_tables"."relname" = 'documents'::"name"))))
UNION ALL
 SELECT 'citations'::"text" AS "table_name",
    'Consider adding index on (document_id, created_at) for chronological ordering'::"text" AS "suggestion"
  WHERE (EXISTS ( SELECT 1
           FROM "pg_stat_user_tables"
          WHERE (("pg_stat_user_tables"."schemaname" = 'public'::"name") AND ("pg_stat_user_tables"."relname" = 'citations'::"name"))))
UNION ALL
 SELECT 'user_subscriptions'::"text" AS "table_name",
    'Consider adding partial index on (user_id) WHERE status = active'::"text" AS "suggestion"
  WHERE (EXISTS ( SELECT 1
           FROM "pg_stat_user_tables"
          WHERE (("pg_stat_user_tables"."schemaname" = 'public'::"name") AND ("pg_stat_user_tables"."relname" = 'user_subscriptions'::"name"))));


ALTER VIEW "public"."query_optimization_suggestions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."query_performance_log" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "query_type" "text" NOT NULL,
    "user_id" "uuid",
    "execution_time_ms" integer NOT NULL,
    "rows_returned" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."query_performance_log" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."query_performance_summary" AS
 SELECT "query_type",
    "count"(*) AS "total_queries",
    "avg"("execution_time_ms") AS "avg_execution_time_ms",
    "max"("execution_time_ms") AS "max_execution_time_ms",
    "min"("execution_time_ms") AS "min_execution_time_ms",
    "percentile_cont"((0.95)::double precision) WITHIN GROUP (ORDER BY (("execution_time_ms")::double precision)) AS "p95_execution_time_ms"
   FROM "public"."query_performance_log"
  WHERE ("created_at" >= ("now"() - '24:00:00'::interval))
  GROUP BY "query_type"
  ORDER BY ("avg"("execution_time_ms")) DESC;


ALTER VIEW "public"."query_performance_summary" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."query_rewrite_suggestions" AS
 SELECT 'documents'::"text" AS "table_name",
    'Use WHERE user_id = $1 AND status IN (''processing'', ''completed'') ORDER BY created_at DESC'::"text" AS "suggested_query",
    'Use idx_documents_list_optimized index'::"text" AS "index_to_use"
UNION ALL
 SELECT 'citations'::"text" AS "table_name",
    'Use WHERE document_id = $1 ORDER BY created_at ASC'::"text" AS "suggested_query",
    'Use idx_citations_doc_time_optimized index'::"text" AS "index_to_use"
UNION ALL
 SELECT 'users'::"text" AS "table_name",
    'Use WHERE id = $1 for auth queries'::"text" AS "suggested_query",
    'Use idx_users_auth_cover index'::"text" AS "index_to_use"
UNION ALL
 SELECT 'user_subscriptions'::"text" AS "table_name",
    'Use WHERE user_id = $1 AND status = ''active'' for subscription queries'::"text" AS "suggested_query",
    'Use idx_user_subscriptions_cover index'::"text" AS "index_to_use";


ALTER VIEW "public"."query_rewrite_suggestions" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."slow_queries_monitor" AS
 SELECT "query",
    "calls",
    "total_exec_time" AS "total_time",
    "mean_exec_time" AS "mean_time",
    "rows",
    ((100.0 * ("shared_blks_hit")::numeric) / (NULLIF(("shared_blks_hit" + "shared_blks_read"), 0))::numeric) AS "hit_percent"
   FROM "extensions"."pg_stat_statements"
  WHERE ("mean_exec_time" > (1000)::double precision)
  ORDER BY "mean_exec_time" DESC
 LIMIT 20;


ALTER VIEW "public"."slow_queries_monitor" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."subscription_plans" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "price_monthly" numeric(10,2) NOT NULL,
    "price_yearly" numeric(10,2),
    "perplexity_queries_daily" integer,
    "excerpt_size_words" integer DEFAULT 150,
    "features" "jsonb" NOT NULL,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."subscription_plans" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."system_catalog_cache" (
    "cache_key" "text" NOT NULL,
    "cached_data" "jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "expires_at" timestamp with time zone DEFAULT ("now"() + '01:00:00'::interval)
);


ALTER TABLE "public"."system_catalog_cache" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."table_size_stats" AS
 SELECT "schemaname",
    "relname" AS "tablename",
    "pg_size_pretty"("pg_total_relation_size"((((("schemaname")::"text" || '.'::"text") || ("relname")::"text"))::"regclass")) AS "size",
    "pg_total_relation_size"((((("schemaname")::"text" || '.'::"text") || ("relname")::"text"))::"regclass") AS "size_bytes",
    "n_tup_ins" AS "inserts",
    "n_tup_upd" AS "updates",
    "n_tup_del" AS "deletes"
   FROM "pg_stat_user_tables"
  WHERE ("schemaname" = 'public'::"name")
  ORDER BY ("pg_total_relation_size"((((("schemaname")::"text" || '.'::"text") || ("relname")::"text"))::"regclass")) DESC;


ALTER VIEW "public"."table_size_stats" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_sessions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "session_start" timestamp with time zone DEFAULT "now"(),
    "session_end" timestamp with time zone,
    "documents_processed" integer DEFAULT 0,
    "citations_generated" integer DEFAULT 0,
    "api_calls_made" integer DEFAULT 0
);


ALTER TABLE "public"."user_sessions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_subscriptions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "stripe_customer_id" "text",
    "stripe_subscription_id" "text",
    "plan_type" "text" NOT NULL,
    "status" "text" DEFAULT 'active'::"text" NOT NULL,
    "current_period_start" timestamp with time zone,
    "current_period_end" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "user_subscriptions_plan_type_check" CHECK (("plan_type" = ANY (ARRAY['free'::"text", 'plus'::"text", 'pro'::"text"]))),
    CONSTRAINT "user_subscriptions_status_check" CHECK (("status" = ANY (ARRAY['active'::"text", 'canceled'::"text", 'past_due'::"text", 'unpaid'::"text"])))
);


ALTER TABLE "public"."user_subscriptions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" NOT NULL,
    "email" "text" NOT NULL,
    "first_name" "text",
    "last_name" "text",
    "subscription_tier" "text" DEFAULT 'free'::"text",
    "subscription_status" "text" DEFAULT 'active'::"text",
    "subscription_end_date" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "last_login" timestamp with time zone,
    "daily_perplexity_queries" integer DEFAULT 0,
    "daily_perplexity_reset_date" "date" DEFAULT CURRENT_DATE,
    "excerpt_size_words" integer DEFAULT 150,
    "plan_type" "text" DEFAULT 'free'::"text",
    "daily_citations_used" integer DEFAULT 0,
    "purchased_citations" integer DEFAULT 0,
    "last_citation_reset_date" "date" DEFAULT CURRENT_DATE,
    CONSTRAINT "users_plan_type_check" CHECK (("plan_type" = ANY (ARRAY['free'::"text", 'plus'::"text", 'pro'::"text"]))),
    CONSTRAINT "users_subscription_status_check" CHECK (("subscription_status" = ANY (ARRAY['active'::"text", 'cancelled'::"text", 'past_due'::"text"]))),
    CONSTRAINT "users_subscription_tier_check" CHECK (("subscription_tier" = ANY (ARRAY['free'::"text", 'plus'::"text", 'pro'::"text"])))
);


ALTER TABLE "public"."users" OWNER TO "postgres";


ALTER TABLE ONLY "public"."api_cache"
    ADD CONSTRAINT "api_cache_cache_key_key" UNIQUE ("cache_key");



ALTER TABLE ONLY "public"."api_cache"
    ADD CONSTRAINT "api_cache_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."api_usage"
    ADD CONSTRAINT "api_usage_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."citation_formats"
    ADD CONSTRAINT "citation_formats_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."citations"
    ADD CONSTRAINT "citations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."query_performance_log"
    ADD CONSTRAINT "query_performance_log_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."subscription_plans"
    ADD CONSTRAINT "subscription_plans_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."subscription_plans"
    ADD CONSTRAINT "subscription_plans_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."system_catalog_cache"
    ADD CONSTRAINT "system_catalog_cache_pkey" PRIMARY KEY ("cache_key");



ALTER TABLE ONLY "public"."user_sessions"
    ADD CONSTRAINT "user_sessions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_subscriptions"
    ADD CONSTRAINT "user_subscriptions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_subscriptions"
    ADD CONSTRAINT "user_subscriptions_user_id_unique" UNIQUE ("user_id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_api_cache_cleanup" ON "public"."api_cache" USING "btree" ("expires_at");



CREATE INDEX "idx_api_cache_lookup" ON "public"."api_cache" USING "btree" ("cache_key", "expires_at");



CREATE INDEX "idx_api_usage_type_time" ON "public"."api_usage" USING "btree" ("api_type", "created_at" DESC);



CREATE INDEX "idx_api_usage_user_time" ON "public"."api_usage" USING "btree" ("user_id", "created_at" DESC);



CREATE INDEX "idx_citation_formats_document" ON "public"."citation_formats" USING "btree" ("document_id", "format_type");



CREATE INDEX "idx_citations_created_at" ON "public"."citations" USING "btree" ("created_at");



CREATE INDEX "idx_citations_doc_created" ON "public"."citations" USING "btree" ("document_id", "created_at");



CREATE INDEX "idx_citations_doc_time_optimized" ON "public"."citations" USING "btree" ("document_id", "created_at", "id");



CREATE INDEX "idx_citations_document_id" ON "public"."citations" USING "btree" ("document_id");



CREATE INDEX "idx_citations_document_optimized" ON "public"."citations" USING "btree" ("document_id", "created_at");



CREATE INDEX "idx_citations_time_series" ON "public"."citations" USING "btree" ("created_at" DESC, "user_id");



CREATE INDEX "idx_citations_title_search" ON "public"."citations" USING "gin" ("to_tsvector"('"english"'::"regconfig", "source_title"));



CREATE INDEX "idx_citations_user_id" ON "public"."citations" USING "btree" ("user_id");



CREATE INDEX "idx_citations_user_stats" ON "public"."citations" USING "btree" ("user_id", "created_at" DESC);



CREATE INDEX "idx_dashboard_user_data" ON "public"."users" USING "btree" ("id", "subscription_tier", "plan_type");



CREATE INDEX "idx_document_details" ON "public"."documents" USING "btree" ("id", "user_id", "status");



CREATE INDEX "idx_documents_created_at" ON "public"."documents" USING "btree" ("created_at");



CREATE INDEX "idx_documents_fulltext" ON "public"."documents" USING "gin" ("to_tsvector"('"english"'::"regconfig", (("title" || ' '::"text") || "content")));



CREATE INDEX "idx_documents_list_optimized" ON "public"."documents" USING "btree" ("user_id", "status", "created_at" DESC) WHERE ("status" = ANY (ARRAY['processing'::"text", 'completed'::"text"]));



CREATE INDEX "idx_documents_search_content" ON "public"."documents" USING "gin" ("to_tsvector"('"english"'::"regconfig", "content"));



CREATE INDEX "idx_documents_search_title" ON "public"."documents" USING "gin" ("to_tsvector"('"english"'::"regconfig", "title"));



CREATE INDEX "idx_documents_status" ON "public"."documents" USING "btree" ("status");



CREATE INDEX "idx_documents_time_series" ON "public"."documents" USING "btree" ("created_at" DESC, "user_id");



CREATE INDEX "idx_documents_user_created" ON "public"."documents" USING "btree" ("user_id", "created_at" DESC);



CREATE INDEX "idx_documents_user_id" ON "public"."documents" USING "btree" ("user_id");



CREATE INDEX "idx_documents_user_optimized" ON "public"."documents" USING "btree" ("user_id", "created_at", "status");



CREATE INDEX "idx_documents_user_status" ON "public"."documents" USING "btree" ("user_id", "status") WHERE ("status" = ANY (ARRAY['processing'::"text", 'completed'::"text"]));



CREATE INDEX "idx_query_performance_type_time" ON "public"."query_performance_log" USING "btree" ("query_type", "created_at" DESC);



CREATE INDEX "idx_system_catalog_cache_expires" ON "public"."system_catalog_cache" USING "btree" ("expires_at");



CREATE INDEX "idx_system_catalog_cache_key" ON "public"."system_catalog_cache" USING "btree" ("cache_key");



CREATE INDEX "idx_user_sessions_active" ON "public"."user_sessions" USING "btree" ("user_id", "session_start" DESC) WHERE ("session_end" IS NULL);



CREATE INDEX "idx_user_sessions_cleanup" ON "public"."user_sessions" USING "btree" ("session_start");



CREATE INDEX "idx_user_subscriptions_active" ON "public"."user_subscriptions" USING "btree" ("user_id", "status", "current_period_end") WHERE ("status" = 'active'::"text");



CREATE UNIQUE INDEX "idx_user_subscriptions_active_user" ON "public"."user_subscriptions" USING "btree" ("user_id") WHERE ("status" = 'active'::"text");



CREATE INDEX "idx_user_subscriptions_cover" ON "public"."user_subscriptions" USING "btree" ("user_id") INCLUDE ("plan_type", "status", "current_period_end", "current_period_start");



CREATE INDEX "idx_user_subscriptions_stripe_subscription_id" ON "public"."user_subscriptions" USING "btree" ("stripe_subscription_id");



CREATE INDEX "idx_user_subscriptions_user_id" ON "public"."user_subscriptions" USING "btree" ("user_id");



CREATE INDEX "idx_user_subscriptions_user_status" ON "public"."user_subscriptions" USING "btree" ("user_id", "status", "plan_type");



CREATE INDEX "idx_users_auth_cover" ON "public"."users" USING "btree" ("id") INCLUDE ("email", "subscription_tier", "plan_type", "daily_citations_used", "purchased_citations");



CREATE INDEX "idx_users_auth_optimized" ON "public"."users" USING "btree" ("id", "subscription_tier", "plan_type");



CREATE INDEX "idx_users_citation_limits" ON "public"."users" USING "btree" ("subscription_tier", "daily_citations_used", "last_citation_reset_date");



CREATE INDEX "idx_users_daily_reset" ON "public"."users" USING "btree" ("last_citation_reset_date");



CREATE INDEX "idx_users_subscription_covering" ON "public"."users" USING "btree" ("id") INCLUDE ("subscription_tier", "plan_type", "updated_at");



CREATE INDEX "idx_users_subscription_tier" ON "public"."users" USING "btree" ("subscription_tier", "daily_citations_used");



CREATE OR REPLACE TRIGGER "update_citation_formats_updated_at" BEFORE UPDATE ON "public"."citation_formats" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_documents_updated_at" BEFORE UPDATE ON "public"."documents" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_user_subscriptions_updated_at" BEFORE UPDATE ON "public"."user_subscriptions" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_users_updated_at" BEFORE UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



ALTER TABLE ONLY "public"."api_usage"
    ADD CONSTRAINT "api_usage_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."citation_formats"
    ADD CONSTRAINT "citation_formats_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "public"."documents"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."citation_formats"
    ADD CONSTRAINT "citation_formats_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."citations"
    ADD CONSTRAINT "citations_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "public"."documents"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."citations"
    ADD CONSTRAINT "citations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_sessions"
    ADD CONSTRAINT "user_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_subscriptions"
    ADD CONSTRAINT "user_subscriptions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



CREATE POLICY "Users can delete citations for their own documents" ON "public"."citations" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."documents"
  WHERE (("documents"."id" = "citations"."document_id") AND ("documents"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can insert citations for their own documents" ON "public"."citations" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."documents"
  WHERE (("documents"."id" = "citations"."document_id") AND ("documents"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can update citations for their own documents" ON "public"."citations" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."documents"
  WHERE (("documents"."id" = "citations"."document_id") AND ("documents"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view their own document citations" ON "public"."citations" FOR SELECT USING (("user_id" = "auth"."uid"()));



ALTER TABLE "public"."api_cache" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "api_cache_authenticated_optimized" ON "public"."api_cache" USING ((( SELECT "auth"."role"() AS "role") = 'authenticated'::"text"));



ALTER TABLE "public"."api_usage" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "api_usage_insert_optimized" ON "public"."api_usage" FOR INSERT WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "api_usage_select_optimized" ON "public"."api_usage" FOR SELECT USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "api_usage_service_role_optimized" ON "public"."api_usage" USING ((( SELECT "auth"."role"() AS "role") = 'service_role'::"text"));



ALTER TABLE "public"."citation_formats" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "citation_formats_delete_optimized" ON "public"."citation_formats" FOR DELETE USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "citation_formats_insert_optimized" ON "public"."citation_formats" FOR INSERT WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "citation_formats_select_optimized" ON "public"."citation_formats" FOR SELECT USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "citation_formats_service_role_optimized" ON "public"."citation_formats" USING ((( SELECT "auth"."role"() AS "role") = 'service_role'::"text"));



CREATE POLICY "citation_formats_update_optimized" ON "public"."citation_formats" FOR UPDATE USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



ALTER TABLE "public"."citations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."documents" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "documents_delete_optimized" ON "public"."documents" FOR DELETE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "documents_insert_optimized" ON "public"."documents" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "documents_select_optimized" ON "public"."documents" FOR SELECT TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "documents_service_role_optimized" ON "public"."documents" TO "service_role" USING ((( SELECT "auth"."role"() AS "role") = 'service_role'::"text"));



CREATE POLICY "documents_update_optimized" ON "public"."documents" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



ALTER TABLE "public"."subscription_plans" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_sessions" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "user_sessions_insert_optimized" ON "public"."user_sessions" FOR INSERT WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "user_sessions_select_optimized" ON "public"."user_sessions" FOR SELECT USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "user_sessions_service_role_optimized" ON "public"."user_sessions" USING ((( SELECT "auth"."role"() AS "role") = 'service_role'::"text"));



CREATE POLICY "user_sessions_update_optimized" ON "public"."user_sessions" FOR UPDATE USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



ALTER TABLE "public"."user_subscriptions" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "user_subscriptions_insert_optimized" ON "public"."user_subscriptions" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "user_subscriptions_select_optimized" ON "public"."user_subscriptions" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "user_subscriptions_service_role_optimized" ON "public"."user_subscriptions" TO "service_role" USING ((( SELECT "auth"."role"() AS "role") = 'service_role'::"text"));



CREATE POLICY "user_subscriptions_update_optimized" ON "public"."user_subscriptions" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



ALTER TABLE "public"."users" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "users_insert_permissive" ON "public"."users" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "users_select_optimized" ON "public"."users" FOR SELECT TO "authenticated" USING (("auth"."uid"() = "id"));



CREATE POLICY "users_service_role_optimized" ON "public"."users" TO "service_role" USING ((( SELECT "auth"."role"() AS "role") = 'service_role'::"text"));



CREATE POLICY "users_update_optimized" ON "public"."users" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "id"));





ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."users";



GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";

























































































































































GRANT ALL ON FUNCTION "public"."add_document_citations"("doc_id" "uuid", "user_uuid" "uuid", "citations_data" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."add_document_citations"("doc_id" "uuid", "user_uuid" "uuid", "citations_data" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_document_citations"("doc_id" "uuid", "user_uuid" "uuid", "citations_data" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."clean_expired_cache"() TO "anon";
GRANT ALL ON FUNCTION "public"."clean_expired_cache"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."clean_expired_cache"() TO "service_role";



GRANT ALL ON FUNCTION "public"."cleanup_old_data"() TO "anon";
GRANT ALL ON FUNCTION "public"."cleanup_old_data"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."cleanup_old_data"() TO "service_role";



GRANT ALL ON FUNCTION "public"."cleanup_system_catalog_cache"() TO "anon";
GRANT ALL ON FUNCTION "public"."cleanup_system_catalog_cache"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."cleanup_system_catalog_cache"() TO "service_role";



GRANT ALL ON FUNCTION "public"."clear_document_citations"("doc_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."clear_document_citations"("doc_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."clear_document_citations"("doc_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."consume_citation"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."consume_citation"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."consume_citation"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."consume_citation"("user_id" "uuid", "count_to_consume" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."consume_citation"("user_id" "uuid", "count_to_consume" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."consume_citation"("user_id" "uuid", "count_to_consume" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."extract_names_from_oauth"("meta_data" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."extract_names_from_oauth"("meta_data" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."extract_names_from_oauth"("meta_data" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_available_citations"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_available_citations"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_available_citations"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_available_citations_optimized"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_available_citations_optimized"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_available_citations_optimized"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_cached_system_data"("key" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_cached_system_data"("key" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_cached_system_data"("key" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_document_citations"("doc_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_document_citations"("doc_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_document_citations"("doc_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_auth_data"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_auth_data"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_auth_data"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_subscription_data"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_subscription_data"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_subscription_data"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_auth_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_auth_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_auth_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."maintain_database_performance"() TO "anon";
GRANT ALL ON FUNCTION "public"."maintain_database_performance"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."maintain_database_performance"() TO "service_role";



GRANT ALL ON FUNCTION "public"."reset_daily_perplexity_queries"() TO "anon";
GRANT ALL ON FUNCTION "public"."reset_daily_perplexity_queries"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."reset_daily_perplexity_queries"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_cached_system_data"("key" "text", "data" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."set_cached_system_data"("key" "text", "data" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_cached_system_data"("key" "text", "data" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."test_auth_performance"() TO "anon";
GRANT ALL ON FUNCTION "public"."test_auth_performance"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."test_auth_performance"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";


















GRANT ALL ON TABLE "public"."api_cache" TO "anon";
GRANT ALL ON TABLE "public"."api_cache" TO "authenticated";
GRANT ALL ON TABLE "public"."api_cache" TO "service_role";



GRANT ALL ON TABLE "public"."api_usage" TO "anon";
GRANT ALL ON TABLE "public"."api_usage" TO "authenticated";
GRANT ALL ON TABLE "public"."api_usage" TO "service_role";



GRANT ALL ON TABLE "public"."citation_formats" TO "anon";
GRANT ALL ON TABLE "public"."citation_formats" TO "authenticated";
GRANT ALL ON TABLE "public"."citation_formats" TO "service_role";



GRANT ALL ON TABLE "public"."citations" TO "anon";
GRANT ALL ON TABLE "public"."citations" TO "authenticated";
GRANT ALL ON TABLE "public"."citations" TO "service_role";



GRANT ALL ON TABLE "public"."documents" TO "anon";
GRANT ALL ON TABLE "public"."documents" TO "authenticated";
GRANT ALL ON TABLE "public"."documents" TO "service_role";



GRANT ALL ON TABLE "public"."index_usage_stats" TO "anon";
GRANT ALL ON TABLE "public"."index_usage_stats" TO "authenticated";
GRANT ALL ON TABLE "public"."index_usage_stats" TO "service_role";



GRANT ALL ON TABLE "public"."query_optimization_suggestions" TO "anon";
GRANT ALL ON TABLE "public"."query_optimization_suggestions" TO "authenticated";
GRANT ALL ON TABLE "public"."query_optimization_suggestions" TO "service_role";



GRANT ALL ON TABLE "public"."query_performance_log" TO "anon";
GRANT ALL ON TABLE "public"."query_performance_log" TO "authenticated";
GRANT ALL ON TABLE "public"."query_performance_log" TO "service_role";



GRANT ALL ON TABLE "public"."query_performance_summary" TO "anon";
GRANT ALL ON TABLE "public"."query_performance_summary" TO "authenticated";
GRANT ALL ON TABLE "public"."query_performance_summary" TO "service_role";



GRANT ALL ON TABLE "public"."query_rewrite_suggestions" TO "anon";
GRANT ALL ON TABLE "public"."query_rewrite_suggestions" TO "authenticated";
GRANT ALL ON TABLE "public"."query_rewrite_suggestions" TO "service_role";



GRANT ALL ON TABLE "public"."slow_queries_monitor" TO "anon";
GRANT ALL ON TABLE "public"."slow_queries_monitor" TO "authenticated";
GRANT ALL ON TABLE "public"."slow_queries_monitor" TO "service_role";



GRANT ALL ON TABLE "public"."subscription_plans" TO "anon";
GRANT ALL ON TABLE "public"."subscription_plans" TO "authenticated";
GRANT ALL ON TABLE "public"."subscription_plans" TO "service_role";



GRANT ALL ON TABLE "public"."system_catalog_cache" TO "anon";
GRANT ALL ON TABLE "public"."system_catalog_cache" TO "authenticated";
GRANT ALL ON TABLE "public"."system_catalog_cache" TO "service_role";



GRANT ALL ON TABLE "public"."table_size_stats" TO "anon";
GRANT ALL ON TABLE "public"."table_size_stats" TO "authenticated";
GRANT ALL ON TABLE "public"."table_size_stats" TO "service_role";



GRANT ALL ON TABLE "public"."user_sessions" TO "anon";
GRANT ALL ON TABLE "public"."user_sessions" TO "authenticated";
GRANT ALL ON TABLE "public"."user_sessions" TO "service_role";



GRANT ALL ON TABLE "public"."user_subscriptions" TO "anon";
GRANT ALL ON TABLE "public"."user_subscriptions" TO "authenticated";
GRANT ALL ON TABLE "public"."user_subscriptions" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "service_role";






























RESET ALL;
