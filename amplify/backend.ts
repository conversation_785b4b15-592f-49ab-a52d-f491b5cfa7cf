import { defineBackend } from '@aws-amplify/backend';
import { FunctionUrl, FunctionUrlAuthType } from 'aws-cdk-lib/aws-lambda';
import { Stack } from 'aws-cdk-lib';

// 导入三个函数的 resource 工厂
import { citationFunction } from './functions/call-function/resource';
import { pdfGeneratorFunction } from './functions/pdfGenerator/resource';
import { pdfProcessorFunction } from './functions/pdfProcessor/resource';

const backend = defineBackend({
  citationFunction,
  pdfGeneratorFunction,
  pdfProcessorFunction,
});

const backendStack: Stack = backend.stack;

// 直接使用 backend 中的 Lambda 函数，而不是通过 ARN 引用
const citationUrl = new FunctionUrl(backendStack, 'CitationFunctionUrl', {
  authType: FunctionUrlAuthType.NONE,
  function: backend.citationFunction.resources.lambda, // 直接使用函数对象
});

const pdfUrl = new FunctionUrl(backendStack, 'PdfFunctionUrl', {
  authType: FunctionUrlAuthType.NONE,
  function: backend.pdfGeneratorFunction.resources.lambda, // 直接使用函数对象
});

const pdfProcessorUrl = new FunctionUrl(backendStack, 'PdfProcessorFunctionUrl', {
  authType: FunctionUrlAuthType.NONE,
  function: backend.pdfProcessorFunction.resources.lambda, // 直接使用函数对象
});

// 添加输出
backend.addOutput({
  custom: {
    citationFunctionUrl: citationUrl.url,
    pdfGeneratorUrl: pdfUrl.url,
    pdfProcessorUrl: pdfProcessorUrl.url,
  }
});

console.log('Citation function ARN:', backend.citationFunction.resources.lambda.functionArn);
console.log('Citation URL:', citationUrl.url);