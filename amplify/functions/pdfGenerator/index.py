"""
PDF生成Lambda函数
为Plus和Pro用户生成引用PDF文件
使用简单的HTML到PDF转换，避免复杂依赖
"""

import json
import os
import sys
import uuid
import boto3
from botocore.exceptions import ClientError
import base64

# 初始化S3客户端
s3_client = boto3.client('s3')

def format_citation_mla(item):
    """格式化单个引用为MLA格式"""
    try:
        # 提取作者信息
        authors = []
        if 'author' in item and isinstance(item['author'], list):
            for author in item['author']:
                if isinstance(author, dict):
                    family = author.get('family', '')
                    given = author.get('given', '')
                    if family and given:
                        authors.append(f"{family}, {given}")
                    elif family:
                        authors.append(family)

        # 格式化作者列表
        if len(authors) == 1:
            author_str = authors[0]
        elif len(authors) == 2:
            author_str = f"{authors[0]} and {authors[1]}"
        elif len(authors) > 2:
            author_str = f"{authors[0]}, et al."
        else:
            author_str = "Unknown Author"

        # 提取标题
        title = item.get('title', 'Untitled')

        # 提取发布信息
        container = item.get('container-title', '')
        issued = item.get('issued', {})
        year = ''
        if isinstance(issued, dict) and 'date-parts' in issued:
            date_parts = issued['date-parts']
            if date_parts and len(date_parts[0]) > 0:
                year = str(date_parts[0][0])

        # 构建MLA格式引用
        citation = f"{author_str}. \"{title}.\""
        if container:
            citation += f" <i>{container}</i>,"
        if year:
            citation += f" {year}."

        return citation
    except Exception as e:
        print(f"Error formatting citation: {e}", file=sys.stderr)
        return f"Error formatting citation: {item.get('title', 'Unknown')}"

def format_citation_apa(item):
    """格式化单个引用为APA格式"""
    try:
        # 提取作者信息
        authors = []
        if 'author' in item and isinstance(item['author'], list):
            for author in item['author']:
                if isinstance(author, dict):
                    family = author.get('family', '')
                    given = author.get('given', '')
                    if family and given:
                        # APA格式：姓, 名的首字母.
                        given_initial = given[0].upper() + '.' if given else ''
                        authors.append(f"{family}, {given_initial}")
                    elif family:
                        authors.append(family)

        # 格式化作者列表
        if len(authors) == 1:
            author_str = authors[0]
        elif len(authors) == 2:
            author_str = f"{authors[0]} & {authors[1]}"
        elif len(authors) > 2:
            author_str = f"{authors[0]}, et al."
        else:
            author_str = "Unknown Author"

        # 提取年份
        issued = item.get('issued', {})
        year = ''
        if isinstance(issued, dict) and 'date-parts' in issued:
            date_parts = issued['date-parts']
            if date_parts and len(date_parts[0]) > 0:
                year = str(date_parts[0][0])

        # 提取标题
        title = item.get('title', 'Untitled')

        # 提取期刊信息
        container = item.get('container-title', '')

        # 构建APA格式引用
        citation = f"{author_str} ({year}). {title}."
        if container:
            citation += f" <i>{container}</i>."

        return citation
    except Exception as e:
        print(f"Error formatting citation: {e}", file=sys.stderr)
        return f"Error formatting citation: {item.get('title', 'Unknown')}"

def generate_html_content(citations, citation_format):
    """生成HTML格式的引用内容"""
    try:
        format_name = "MLA" if citation_format.lower() == "mla" else "APA"

        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Works Cited ({format_name} Format)</title>
    <style>
        body {{
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 2.0;
            margin: 1in;
            color: #000;
        }}
        h1 {{
            font-size: 12pt;
            font-weight: normal;
            text-align: center;
            margin-bottom: 1in;
        }}
        .citation {{
            margin-bottom: 0;
            text-indent: -0.5in;
            margin-left: 0.5in;
        }}
        @media print {{
            body {{ margin: 1in; }}
            .citation {{ page-break-inside: avoid; }}
        }}
    </style>
</head>
<body>
    <h1>Works Cited</h1>
"""

        for citation in citations:
            # 清理HTML标签但保留斜体
            clean_citation = citation.replace('<i>', '<em>').replace('</i>', '</em>')
            html_content += f'    <p class="citation">{clean_citation}</p>\n'

        html_content += """
</body>
</html>
"""

        return html_content

    except Exception as e:
        print(f"Error generating HTML: {e}", file=sys.stderr)
        raise

def save_html_as_file(html_content, output_path):
    """将HTML内容保存为文件"""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"HTML file generated successfully at {output_path}", file=sys.stderr)

    except Exception as e:
        print(f"Error saving HTML file: {e}", file=sys.stderr)
        raise

def process_and_generate_html(csl_items: list, style_choice: str) -> str:
    """
    从CSL数据生成HTML文件并返回本地临时路径
    """
    try:
        # 过滤掉无效的条目
        valid_items = [item for item in csl_items if isinstance(item, dict) and item.get("title")]

        if not valid_items:
            raise ValueError("No valid citation items with a title were provided.")

        # 格式化引用
        citations = []
        for item in valid_items:
            if style_choice.lower() == "apa":
                citation = format_citation_apa(item)
            else:  # 默认MLA
                citation = format_citation_mla(item)
            citations.append(citation)

        # 按字母顺序排序
        citations.sort()

        # 生成HTML
        html_content = generate_html_content(citations, style_choice)
        temp_html_path = f"/tmp/{uuid.uuid4()}.html"
        save_html_as_file(html_content, temp_html_path)

        return temp_html_path

    except Exception as e:
        print(f"Error in process_and_generate_html: {e}", file=sys.stderr)
        raise

def handler(event, context):
    """
    AWS Lambda处理程序，用于从给定的CSL数据生成PDF。
    """
    try:
        print(f"Received event: {json.dumps(event)}", file=sys.stderr)

        # 从API Gateway传入的请求体中解析输入
        if 'body' in event:
            request_data = json.loads(event.get('body', '{}'))
        else:
            request_data = event

        # 支持两种参数格式：csl_items 或 citations
        csl_items = request_data.get('csl_items') or request_data.get('citations')
        citation_format = request_data.get('citation_format', 'mla')

        if not csl_items or not isinstance(csl_items, list):
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Missing or invalid "csl_items" or "citations" in request body.'})
            }

        # 1. 调用核心逻辑在 /tmp 目录生成HTML文件
        local_html_path = process_and_generate_html(csl_items, citation_format)

        # 2. 读取HTML内容
        with open(local_html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        # 3. 清理本地临时文件
        os.remove(local_html_path)

        # 4. 返回HTML内容供前端处理
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'message': 'Citations formatted successfully.',
                'html_content': html_content,
                'citation_format': citation_format
            })
        }

    except Exception as e:
        print(f"Error processing PDF request: {e}", file=sys.stderr)
        return {
            'statusCode': 500,
            'headers': { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
            'body': json.dumps({'error': str(e)})
        }
