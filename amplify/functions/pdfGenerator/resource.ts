import { defineFunction } from '@aws-amplify/backend';
import { Duration } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { PolicyStatement, Effect } from 'aws-cdk-lib/aws-iam';
import { Function, Runtime, Code, LayerVersion } from 'aws-cdk-lib/aws-lambda';
import path from 'path';
import { fileURLToPath } from 'url';

const NLTK_LAYER_ARN = 'arn:aws:lambda:us-east-2:271134112239:layer:citeai-python-dependency:7';
const S3_BUCKET_NAME = 'citeai-pdf-outputs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const pdfGeneratorFunction = defineFunction((scope: Construct) => {
  const nltkLayer = LayerVersion.fromLayerVersionArn(scope, 'NLTKLayerReference', NLTK_LAYER_ARN);

  const func = new Function(scope, 'PdfGeneratorFunction', {
    functionName: 'PdfGeneratorFunction',
    runtime: Runtime.PYTHON_3_12,
    handler: 'index.handler',
    code: Code.fromAsset(path.join(__dirname)), // 保持目录结构：index.py + helpers + requirements.txt
    memorySize: 1024,
    timeout: Duration.seconds(60),
    environment: {
      S3_BUCKET_NAME,
    },
    layers: [nltkLayer],
  });

  func.addToRolePolicy(new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ['s3:PutObject', 's3:GetObject'],
    resources: [`arn:aws:s3:::${S3_BUCKET_NAME}/*`],
  }));

  func.addToRolePolicy(new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ['s3:ListBucket'],
    resources: [`arn:aws:s3:::${S3_BUCKET_NAME}`],
  }));

  return func;
});
