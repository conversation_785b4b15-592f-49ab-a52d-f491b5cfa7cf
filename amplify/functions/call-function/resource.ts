import { defineFunction } from '@aws-amplify/backend';
import { Duration } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { PolicyStatement, Effect } from 'aws-cdk-lib/aws-iam';
import { Function, Runtime, Code, LayerVersion } from 'aws-cdk-lib/aws-lambda';

const NLTK_LAYER_ARN = 'arn:aws:lambda:us-east-2:271134112239:layer:citeai-python-dependency:7';
const S3_BUCKET_NAME = 'citeai-pdf-outputs';

export const citationFunction = defineFunction((scope: Construct) => {
  const nltkLayer = LayerVersion.fromLayerVersionArn(scope, 'NLTKLayer', NLTK_LAYER_ARN);

  const func = new Function(scope, 'CallFunction', {
    functionName: 'CallFunction',
    runtime: Runtime.PYTHON_3_12,
    handler: 'index.handler',
    code: Code.fromAsset(import.meta.dirname),
    memorySize: 512,
    timeout: Duration.seconds(30),
    environment: {
      S3_BUCKET_NAME,
    },
    layers: [nltkLayer],
  });

  func.addToRolePolicy(new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ['s3:PutObject', 's3:GetObject'],
    resources: [`arn:aws:s3:::${S3_BUCKET_NAME}/*`],
  }));

  // 若需要列出桶，额外添加：
  func.addToRolePolicy(new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ['s3:ListBucket'],
    resources: [`arn:aws:s3:::${S3_BUCKET_NAME}`],
  }));

  return func;
});
