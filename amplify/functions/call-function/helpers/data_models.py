#!/usr/bin/env python3
"""
CiteAI Data Models
This module provides data models that correspond to the Supabase database schema.
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import json
import hashlib


class SubscriptionTier(str, Enum):
    FREE = "free"
    PLUS = "plus"
    PRO = "pro"


class SubscriptionStatus(str, Enum):
    ACTIVE = "active"
    CANCELLED = "cancelled"
    PAST_DUE = "past_due"


class DocumentStatus(str, Enum):
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class CacheType(str, Enum):
    PERPLEXITY = "perplexity"
    SEMANTIC_SCHOLAR = "semantic_scholar"
    WEB_SCRAPING = "web_scraping"


class CitationFormatType(str, Enum):
    APA = "apa"
    MLA = "mla"
    CHICAGO = "chicago"
    HARVARD = "harvard"


class ApiType(str, Enum):
    PERPLEXITY = "perplexity"
    SEMANTIC_SCHOLAR = "semantic_scholar"


@dataclass
class User:
    """User model representing a CiteAI user."""
    id: str
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    avatar_url: Optional[str] = None
    subscription_tier: SubscriptionTier = SubscriptionTier.FREE
    subscription_status: SubscriptionStatus = SubscriptionStatus.ACTIVE
    subscription_end_date: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None
    citation_count: int = 0
    daily_perplexity_queries: int = 0
    daily_perplexity_reset_date: date = field(default_factory=date.today)
    excerpt_size_words: int = 150

    def can_generate_citations(self) -> bool:
        """Check if user can generate more citations based on their subscription."""
        if self.subscription_tier == SubscriptionTier.PRO:
            return True
        elif self.subscription_tier == SubscriptionTier.PLUS:
            return self.daily_perplexity_queries < 20
        else:
            return self.daily_perplexity_queries < 6

    def get_excerpt_size(self) -> int:
        """Get the excerpt size based on subscription tier."""
        if self.subscription_tier == SubscriptionTier.FREE:
            return 150
        else:
            return 500

    def increment_citation_count(self):
        """Increment the user's citation count and daily perplexity queries."""
        self.citation_count += 1
        self.daily_perplexity_queries += 1
        self.updated_at = datetime.utcnow()

    def reset_daily_queries(self):
        """Reset the daily perplexity queries count."""
        self.daily_perplexity_queries = 0
        self.daily_perplexity_reset_date = date.today()
        self.updated_at = datetime.utcnow()

    def to_dict(self) -> Dict[str, Any]:
        """Convert user to dictionary for database operations."""
        return {
            "id": self.id,
            "email": self.email,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "avatar_url": self.avatar_url,
            "subscription_tier": self.subscription_tier.value,
            "subscription_status": self.subscription_status.value,
            "subscription_end_date": self.subscription_end_date.isoformat() if self.subscription_end_date else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "citation_count": self.citation_count,
            "daily_perplexity_queries": self.daily_perplexity_queries,
            "daily_perplexity_reset_date": self.daily_perplexity_reset_date.isoformat(),
            "excerpt_size_words": self.excerpt_size_words
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create user from dictionary."""
        return cls(
            id=data["id"],
            email=data["email"],
            first_name=data.get("first_name"),
            last_name=data.get("last_name"),
            avatar_url=data.get("avatar_url"),
            subscription_tier=SubscriptionTier(data.get("subscription_tier", "free")),
            subscription_status=SubscriptionStatus(data.get("subscription_status", "active")),
            subscription_end_date=datetime.fromisoformat(data["subscription_end_date"]) if data.get("subscription_end_date") else None,
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            last_login=datetime.fromisoformat(data["last_login"]) if data.get("last_login") else None,
            citation_count=data.get("citation_count", 0),
            daily_perplexity_queries=data.get("daily_perplexity_queries", 0),
            daily_perplexity_reset_date=date.fromisoformat(data.get("daily_perplexity_reset_date", date.today().isoformat())),
            excerpt_size_words=data.get("excerpt_size_words", 150)
        )


@dataclass
class Document:
    """Document model representing a user's uploaded essay/paper."""
    id: str
    user_id: str
    title: str
    content: str
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    word_count: Optional[int] = None
    topic: Optional[str] = None
    status: DocumentStatus = DocumentStatus.PROCESSING
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    processed_at: Optional[datetime] = None
    flagged_sentences_count: int = 0
    citations_generated_count: int = 0

    def calculate_word_count(self) -> int:
        """Calculate word count from content."""
        return len(self.content.split())

    def mark_as_processed(self):
        """Mark document as processed."""
        self.status = DocumentStatus.COMPLETED
        self.processed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()

    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary for database operations."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "title": self.title,
            "content": self.content,
            "file_name": self.file_name,
            "file_size": self.file_size,
            "word_count": self.word_count or self.calculate_word_count(),
            "topic": self.topic,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "flagged_sentences_count": self.flagged_sentences_count,
            "citations_generated_count": self.citations_generated_count
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Document':
        """Create document from dictionary."""
        return cls(
            id=data["id"],
            user_id=data["user_id"],
            title=data["title"],
            content=data["content"],
            file_name=data.get("file_name"),
            file_size=data.get("file_size"),
            word_count=data.get("word_count"),
            topic=data.get("topic"),
            status=DocumentStatus(data.get("status", "processing")),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            processed_at=datetime.fromisoformat(data["processed_at"]) if data.get("processed_at") else None,
            flagged_sentences_count=data.get("flagged_sentences_count", 0),
            citations_generated_count=data.get("citations_generated_count", 0)
        )


@dataclass
class ApiCache:
    """API Cache model for storing API responses to reduce API calls."""
    id: str
    cache_key: str
    cache_type: CacheType
    request_data: Dict[str, Any]
    response_data: Dict[str, Any]
    url: Optional[str] = None
    title: Optional[str] = None
    is_paper: Optional[bool] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    expires_at: datetime = field(default_factory=lambda: datetime.utcnow().replace(day=datetime.utcnow().day + 30))
    hit_count: int = 1

    @classmethod
    def create_cache_key(cls, cache_type: CacheType, request_data: Dict[str, Any]) -> str:
        """Create a unique cache key based on request data."""
        # Create a hash of the request data for consistent caching
        request_str = json.dumps(request_data, sort_keys=True)
        return f"{cache_type.value}:{hashlib.md5(request_str.encode()).hexdigest()}"

    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        return datetime.utcnow() > self.expires_at

    def increment_hit_count(self):
        """Increment the hit count for this cache entry."""
        self.hit_count += 1

    def to_dict(self) -> Dict[str, Any]:
        """Convert cache entry to dictionary for database operations."""
        return {
            "id": self.id,
            "cache_key": self.cache_key,
            "cache_type": self.cache_type.value,
            "request_data": self.request_data,
            "response_data": self.response_data,
            "url": self.url,
            "title": self.title,
            "is_paper": self.is_paper,
            "created_at": self.created_at.isoformat(),
            "expires_at": self.expires_at.isoformat(),
            "hit_count": self.hit_count
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ApiCache':
        """Create cache entry from dictionary."""
        return cls(
            id=data["id"],
            cache_key=data["cache_key"],
            cache_type=CacheType(data["cache_type"]),
            request_data=data["request_data"],
            response_data=data["response_data"],
            url=data.get("url"),
            title=data.get("title"),
            is_paper=data.get("is_paper"),
            created_at=datetime.fromisoformat(data["created_at"]),
            expires_at=datetime.fromisoformat(data["expires_at"]),
            hit_count=data.get("hit_count", 1)
        )


@dataclass
class Citation:
    """Citation model representing a generated citation."""
    id: str
    document_id: str
    user_id: str
    source_url: Optional[str] = None
    source_title: str = ""
    source_author: Optional[str] = None
    source_date: Optional[str] = None
    source_venue: Optional[str] = None
    source_doi: Optional[str] = None
    source_site_name: Optional[str] = None
    is_research_paper: bool = False
    csl_data: Dict[str, Any] = field(default_factory=dict)
    sentence_index: Optional[int] = None
    context_text: Optional[str] = None
    cache_id: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        """Convert citation to dictionary for database operations."""
        return {
            "id": self.id,
            "document_id": self.document_id,
            "user_id": self.user_id,
            "cache_id": self.cache_id,
            "source_url": self.source_url,
            "source_title": self.source_title,
            "source_author": self.source_author,
            "source_date": self.source_date,
            "source_venue": self.source_venue,
            "source_doi": self.source_doi,
            "source_site_name": self.source_site_name,
            "is_research_paper": self.is_research_paper,
            "csl_data": self.csl_data,
            "sentence_index": self.sentence_index,
            "context_text": self.context_text,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Citation':
        """Create citation from dictionary."""
        return cls(
            id=data["id"],
            document_id=data["document_id"],
            user_id=data["user_id"],
            cache_id=data.get("cache_id"),
            source_url=data.get("source_url"),
            source_title=data.get("source_title", ""),
            source_author=data.get("source_author"),
            source_date=data.get("source_date"),
            source_venue=data.get("source_venue"),
            source_doi=data.get("source_doi"),
            source_site_name=data.get("source_site_name"),
            is_research_paper=data.get("is_research_paper", False),
            csl_data=data.get("csl_data", {}),
            sentence_index=data.get("sentence_index"),
            context_text=data.get("context_text"),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )


@dataclass
class CitationFormat:
    """Citation format model for storing generated bibliographies."""
    id: str
    document_id: str
    user_id: str
    format_type: CitationFormatType
    bibliography_html: str
    bibliography_pdf_url: Optional[str] = None
    citation_count: int = 0
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        """Convert citation format to dictionary for database operations."""
        return {
            "id": self.id,
            "document_id": self.document_id,
            "user_id": self.user_id,
            "format_type": self.format_type.value,
            "bibliography_html": self.bibliography_html,
            "bibliography_pdf_url": self.bibliography_pdf_url,
            "citation_count": self.citation_count,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CitationFormat':
        """Create citation format from dictionary."""
        return cls(
            id=data["id"],
            document_id=data["document_id"],
            user_id=data["user_id"],
            format_type=CitationFormatType(data["format_type"]),
            bibliography_html=data["bibliography_html"],
            bibliography_pdf_url=data.get("bibliography_pdf_url"),
            citation_count=data.get("citation_count", 0),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )


@dataclass
class ApiUsage:
    """API usage tracking model."""
    id: str
    user_id: str
    api_type: ApiType
    request_count: int = 1
    cost_usd: float = 0.0
    created_at: datetime = field(default_factory=datetime.utcnow)
    month_year: str = field(default_factory=lambda: datetime.utcnow().strftime("%Y-%m"))

    def to_dict(self) -> Dict[str, Any]:
        """Convert API usage to dictionary for database operations."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "api_type": self.api_type.value,
            "request_count": self.request_count,
            "cost_usd": self.cost_usd,
            "created_at": self.created_at.isoformat(),
            "month_year": self.month_year
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ApiUsage':
        """Create API usage from dictionary."""
        return cls(
            id=data["id"],
            user_id=data["user_id"],
            api_type=ApiType(data["api_type"]),
            request_count=data.get("request_count", 1),
            cost_usd=data.get("cost_usd", 0.0),
            created_at=datetime.fromisoformat(data["created_at"]),
            month_year=data.get("month_year", datetime.utcnow().strftime("%Y-%m"))
        )


@dataclass
class UserSession:
    """User session tracking model."""
    id: str
    user_id: str
    session_start: datetime = field(default_factory=datetime.utcnow)
    session_end: Optional[datetime] = None
    documents_processed: int = 0
    citations_generated: int = 0
    api_calls_made: int = 0

    def end_session(self):
        """End the user session."""
        self.session_end = datetime.utcnow()

    def get_session_duration(self) -> Optional[float]:
        """Get session duration in seconds."""
        if self.session_end:
            return (self.session_end - self.session_start).total_seconds()
        return None

    def to_dict(self) -> Dict[str, Any]:
        """Convert user session to dictionary for database operations."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "session_start": self.session_start.isoformat(),
            "session_end": self.session_end.isoformat() if self.session_end else None,
            "documents_processed": self.documents_processed,
            "citations_generated": self.citations_generated,
            "api_calls_made": self.api_calls_made
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserSession':
        """Create user session from dictionary."""
        return cls(
            id=data["id"],
            user_id=data["user_id"],
            session_start=datetime.fromisoformat(data["session_start"]),
            session_end=datetime.fromisoformat(data["session_end"]) if data.get("session_end") else None,
            documents_processed=data.get("documents_processed", 0),
            citations_generated=data.get("citations_generated", 0),
            api_calls_made=data.get("api_calls_made", 0)
        )


@dataclass
class SubscriptionPlan:
    """Subscription plan model."""
    id: str
    name: str
    price_monthly: float
    price_yearly: Optional[float] = None
    perplexity_queries_daily: Optional[int] = None
    excerpt_size_words: int = 150
    features: Dict[str, Any] = field(default_factory=dict)
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        """Convert subscription plan to dictionary for database operations."""
        return {
            "id": self.id,
            "name": self.name,
            "price_monthly": self.price_monthly,
            "price_yearly": self.price_yearly,
            "perplexity_queries_daily": self.perplexity_queries_daily,
            "excerpt_size_words": self.excerpt_size_words,
            "features": self.features,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat()
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SubscriptionPlan':
        """Create subscription plan from dictionary."""
        return cls(
            id=data["id"],
            name=data["name"],
            price_monthly=data["price_monthly"],
            price_yearly=data.get("price_yearly"),
            perplexity_queries_daily=data.get("perplexity_queries_daily"),
            excerpt_size_words=data.get("excerpt_size_words", 150),
            features=data.get("features", {}),
            is_active=data.get("is_active", True),
            created_at=datetime.fromisoformat(data["created_at"])
        ) 