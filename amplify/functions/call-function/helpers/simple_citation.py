#!/usr/bin/env python3
"""
简化的引用格式化器 - 不依赖citeproc-py和lxml
用于在Lambda环境中生成MLA和APA格式的引用
"""

import re
from datetime import datetime
from urllib.parse import urlparse

def extract_website_name(url):
    """从URL提取网站名称"""
    if not url:
        return "Online Publication"
    
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # 移除www前缀
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # 特殊网站名称映射
        site_names = {
            'un.org': 'United Nations',
            'who.int': 'World Health Organization',
            'unesco.org': 'UNESCO',
            'wikipedia.org': 'Wikipedia',
            'britannica.com': 'Encyclopedia Britannica',
            'nytimes.com': 'The New York Times',
            'washingtonpost.com': 'The Washington Post',
            'bbc.com': 'BBC',
            'cnn.com': 'CNN',
            'reuters.com': 'Reuters',
            'nature.com': 'Nature',
            'science.org': 'Science',
            'ncbi.nlm.nih.gov': 'National Center for Biotechnology Information',
            'nih.gov': 'National Institutes of Health',
            'cdc.gov': 'Centers for Disease Control and Prevention',
            'edu': 'Academic Institution'
        }
        
        # 检查特殊映射
        for key, name in site_names.items():
            if key in domain:
                return name
        
        # 默认处理：移除.com/.org等后缀并首字母大写
        domain_parts = domain.split('.')
        if len(domain_parts) > 1:
            main_part = domain_parts[0]
            return main_part.replace('-', ' ').replace('_', ' ').title()
        
        return domain.title()
        
    except Exception:
        return "Online Publication"

def format_author_name(author):
    """格式化作者姓名"""
    if isinstance(author, dict):
        family = author.get('family', '')
        given = author.get('given', '')
        if family and given:
            return f"{family}, {given}"
        elif family:
            return family
        elif given:
            return given
    elif isinstance(author, str):
        return author
    return ""

def format_authors_mla(authors):
    """MLA格式的作者列表"""
    if not authors:
        return ""
    
    formatted_authors = []
    for author in authors:
        name = format_author_name(author)
        if name:
            formatted_authors.append(name)
    
    if not formatted_authors:
        return ""
    
    if len(formatted_authors) == 1:
        return formatted_authors[0]
    elif len(formatted_authors) == 2:
        return f"{formatted_authors[0]} and {formatted_authors[1]}"
    else:
        # 对于3个或更多作者，MLA格式使用"et al."
        return f"{formatted_authors[0]}, et al."

def format_authors_apa(authors):
    """APA格式的作者列表"""
    if not authors:
        return ""
    
    formatted_authors = []
    for author in authors:
        name = format_author_name(author)
        if name:
            # APA格式：姓, 名的首字母.
            if ', ' in name:
                family, given = name.split(', ', 1)
                given_initial = given[0].upper() + '.' if given else ''
                formatted_authors.append(f"{family}, {given_initial}")
            else:
                formatted_authors.append(name)
    
    if not formatted_authors:
        return ""
    
    if len(formatted_authors) == 1:
        return formatted_authors[0]
    elif len(formatted_authors) <= 7:
        if len(formatted_authors) == 2:
            return f"{formatted_authors[0]} & {formatted_authors[1]}"
        else:
            return ", ".join(formatted_authors[:-1]) + f", & {formatted_authors[-1]}"
    else:
        # 超过7个作者使用省略
        return ", ".join(formatted_authors[:6]) + ", ... " + formatted_authors[-1]

def get_current_year():
    """获取当前年份"""
    return datetime.now().year

def format_date(date_info):
    """格式化日期"""
    if not date_info:
        return str(get_current_year())
    
    if isinstance(date_info, dict) and 'date-parts' in date_info:
        date_parts = date_info['date-parts']
        if date_parts and len(date_parts) > 0 and len(date_parts[0]) > 0:
            return str(date_parts[0][0])  # 年份
    
    return str(get_current_year())

def format_citation_mla(item):
    """生成MLA格式的引用"""
    parts = []
    
    # 作者
    authors = item.get('author', [])
    author_str = format_authors_mla(authors)
    if author_str:
        parts.append(f"{author_str}.")
    
    # 标题
    title = item.get('title', '').strip()
    if title:
        # 移除末尾的句号
        title = title.rstrip('.')
        parts.append(f'"{title}."')
    
    # 容器标题（网站名称）
    container = item.get('container-title', '')
    if not container:
        url = item.get('URL', '')
        if url:
            container = extract_website_name(url)
    
    if container:
        parts.append(f"{container},")
    
    # 日期
    date_str = format_date(item.get('issued'))
    if date_str:
        parts.append(f"{date_str},")
    
    # URL
    url = item.get('URL', '')
    if url:
        parts.append(f"{url}.")
    
    return " ".join(parts)

def format_citation_apa(item):
    """生成APA格式的引用"""
    parts = []
    
    # 作者
    authors = item.get('author', [])
    author_str = format_authors_apa(authors)
    if author_str:
        parts.append(f"{author_str}")
    
    # 日期
    date_str = format_date(item.get('issued'))
    if date_str:
        parts.append(f"({date_str}).")
    
    # 标题
    title = item.get('title', '').strip()
    if title:
        title = title.rstrip('.')
        parts.append(f"{title}.")
    
    # 网站名称
    container = item.get('container-title', '')
    if not container:
        url = item.get('URL', '')
        if url:
            container = extract_website_name(url)
    
    if container:
        parts.append(f"{container}.")
    
    # URL
    url = item.get('URL', '')
    if url:
        parts.append(f"Retrieved from {url}")
    
    return " ".join(parts)

def format_citations_simple(csl_items, style="mla"):
    """使用简化格式化器生成引用"""
    if not csl_items:
        return []
    
    formatted_citations = []
    
    for item in csl_items:
        if not isinstance(item, dict):
            continue
        
        try:
            if style.lower() == "apa":
                citation = format_citation_apa(item)
            else:  # 默认MLA
                citation = format_citation_mla(item)
            
            if citation:
                formatted_citations.append(citation)
        except Exception as e:
            print(f"Error formatting citation: {e}")
            continue
    
    return formatted_citations

def generate_html_simple(citations, style="mla"):
    """生成简单的HTML输出"""
    if not citations:
        return "<p>No citations available.</p>"
    
    style_name = "MLA" if style.lower() == "mla" else "APA"
    
    html_parts = [
        f'<div class="citations {style.lower()}">',
        f'<h2>{style_name} Citations</h2>'
    ]
    
    for citation in citations:
        html_parts.append(f'<div class="citation-entry">{citation}</div>')
    
    html_parts.append('</div>')
    
    return "\n".join(html_parts)
