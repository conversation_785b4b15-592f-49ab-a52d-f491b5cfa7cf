import requests
import re
import json
import sys
from bs4 import BeautifulSoup
from typing import Optional, Dict, Any

from .utils import build_csl_json

"""
————————————————————————————————————
————————OPENALEX, CROSSREF, NCBI_IDCONV—————————
————————————————————————————————————
"""
OPENALEX_BASE_URL = "https://api.openalex.org/works"
CROSSREF_BASE_URL = "https://api.crossref.org/works"
NCBI_IDCONV_URL = "https://www.ncbi.nlm.nih.gov/pmc/utils/idconv/v1.0/"

def extract_doi_from_url(url: str, email: str = "<EMAIL>") -> Optional[str]:
    doi_match = re.search(r"(10\.\d{4,9}/[-._;()/:A-Z0-9]+)", url, re.IGNORECASE)
    if doi_match:
        return doi_match.group(1).strip()
    pmid_match = re.search(r"pubmed\.ncbi\.nlm\.nih\.gov/(\d+)", url)
    if pmid_match:
        pmid = pmid_match.group(1)
        return _get_doi_from_ncbi(pmid, email)
    pmcid_match = re.search(r"pmc/articles/(PMC\d+)", url, re.IGNORECASE)
    if pmcid_match:
        pmcid = pmcid_match.group(1)
        return _get_doi_from_ncbi(pmcid, email)
    return None

def _get_doi_from_ncbi(id_str: str, email: str) -> Optional[str]:
    params = {
        "tool": "citeai",
        "email": email,
        "ids": id_str,
        "format": "json"
    }
    resp = requests.get(NCBI_IDCONV_URL, params=params, timeout=10)
    if resp.ok:
        data = resp.json().get("records", [])
        if data and data[0].get("doi"):
            return data[0]["doi"]
    return None

def query_openalex_by_title(title: str, max_results: int = 5) -> Optional[Dict[str, Any]]:
    try:
        params = {
            "search": title,
            "per-page": max_results
        }
        resp = requests.get(OPENALEX_BASE_URL, params=params, timeout=10)
        if not resp.ok:
            return None
        results = resp.json().get("results", [])
        if not results:
            return None
        work = results[0]

        # Safely get venue with fallbacks
        venue = None
        if work.get("host_venue"):
            venue = work.get("host_venue", {}).get("display_name")
        if not venue and work.get("primary_location"):
            venue = work.get("primary_location", {}).get("source", {}).get("display_name")

        # Safely get URL
        url = None
        if work.get("primary_location"):
            url = work.get("primary_location", {}).get("landing_page_url")

        return {
            "title": work.get("display_name"),
            "authors": [a["author"]["display_name"] for a in work.get("authorships", []) if a.get("author")],
            "year": work.get("publication_year"),
            "venue": venue,
            "url": url,
            "externalIds": work.get("ids", {})
        }
    except Exception as e:
        print(f"OpenAlex query failed for title '{title}': {e}", file=sys.stderr)
        return None

def query_crossref_by_doi(doi: str) -> Optional[Dict[str, Any]]:
    try:
        url = f"{CROSSREF_BASE_URL}/{doi}"
        resp = requests.get(url, timeout=10)
        if not resp.ok:
            return None
        data = resp.json().get("message", {})
        return {
            "title": data.get("title", [""])[0],
            "authors": [
                f"{a.get('given', '')} {a.get('family', '')}".strip()
                for a in data.get("author", [])
            ],
            "year": data.get("issued", {}).get("date-parts", [[None]])[0][0],
            "venue": data.get("container-title", [""])[0],
            "url": data.get("URL"),
            "externalIds": {"DOI": data.get("DOI")}
        }
    except Exception as e:
        print(f"Crossref query failed for DOI '{doi}': {e}", file=sys.stderr)
        return None

def fetch_paper_metadata(title: str, url: str) -> Optional[Dict[str, Any]]:
    meta = query_openalex_by_title(title)
    if meta and meta.get("title"):
        return meta
    doi = extract_doi_from_url(url)
    if doi:
        return query_crossref_by_doi(doi)
    return None






"""
————————————————————————————————————————————
————————Functions to scrape the web for information.—————————
————————————————————————————————————————————
"""
def get_title(soup_obj):
    meta_title = soup_obj.find("meta", property="og:title")
    # return title if it exists
    if meta_title and meta_title.get("content"):
        return meta_title["content"]

    # try to find the title looking for the other tags
    possible_titles = soup_obj.find_all(["h1", "h2", "div", "span"])

    for tag in possible_titles:
        classes = tag.get("class", [])  # getting class present in tag
        # if we find any title or headline 
        if any("title" in c.lower() or "headline" in c.lower() for c in classes):
            text = tag.get_text(strip=True)
            return text

    if soup_obj.title:
        return soup_obj.title.get_text(strip=True)
    return None


BAD_AUTHOR_VALUES = {
    "watch live", "read more", "staff", "editor", "editors",
    "live", "breaking news", "by", "byline", "guest author"
}

def get_author(soup_obj):
    nbc_div = soup_obj.find("div", class_="article-inline-byline")
    if nbc_div:
        # find the inner <a> (author link)
        author_link = nbc_div.find("span", class_="byline-name")
        if author_link is not None:
            a_tag = author_link.find("a") if author_link else None
            if a_tag and a_tag.get_text(strip=True):
                # The byline text is already “Firstname Lastname”
                return a_tag.get_text(strip=True)
            
    # 1. JSON-LD: Only accept if "@type": "Person"
    for script in soup_obj.find_all("script", type="application/ld+json"):
        try:
            data = json.loads(script.string)
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict) and "author" in item:
                        author = item["author"]
                        if isinstance(author, dict) and author.get("@type", "").lower() == "person" and author.get("name"):
                            cleaned = _extract_clean_name(author["name"])
                            if cleaned:
                                return cleaned
            elif isinstance(data, dict) and "author" in data:
                author = data["author"]
                if isinstance(author, dict) and author.get("@type", "").lower() == "person" and author.get("name"):
                    cleaned = _extract_clean_name(author["name"])
                    if cleaned:
                        return cleaned
        except Exception:
            continue

    # 2. Look near <time> tag
    time_tag = soup_obj.find("time")
    if time_tag:
        for parent in [time_tag.parent, getattr(time_tag.parent, "parent", None)]:
            if not parent:
                continue
            for tag in parent.find_all(["span", "div", "p"], recursive=False):
                text = tag.get_text(strip=True)
                cleaned = _extract_clean_name(text)
                if cleaned:
                    return cleaned

    # 3. <meta name="author">
    meta_author = soup_obj.find("meta", attrs={"name": "author"})
    if meta_author:
        text = meta_author.get("content", "").strip()
        cleaned = _extract_clean_name(text)
        if cleaned:
            return cleaned

    # 4. Class-based: "author" or "byline"
    for tag in soup_obj.find_all(class_=lambda c: c and ("author" in c.lower() or "byline" in c.lower())):
        text = tag.get_text(strip=True)
        cleaned = _extract_clean_name(text)
        if cleaned:
            return cleaned

    # 5. Links with "/author/", "/by/", etc.
    for tag in soup_obj.find_all("a", href=lambda h: h and any(kw in h.lower() for kw in ["/author/", "/by/", "writer", "people", "profile"])):
        text = tag.get_text(strip=True)
        cleaned = _extract_clean_name(text)
        if cleaned:
            return cleaned

    return None

def _extract_clean_name(author_value):
    """
    Extracts and cleans author name from a string.

    get_auther() 's helper function
    """
    if not author_value:
        return None

    text = author_value.strip()
    # Remove "by" prefix more aggressively
    text = re.sub(r"^by\s*", "", text, flags=re.IGNORECASE)
    if "by" in text.lower():
        parts = re.split(r"\bby\b", text, flags=re.IGNORECASE)
        if len(parts) > 1:
            text = parts[1].strip()

    # Validate that this looks like a real author name
    def is_valid_author_name(name):
        if not name or len(name.strip()) < 2:
            return False
        
        # Skip if contains numbers (like "Date: Sept. 14" -> "Date S.")
        if any(char.isdigit() for char in name):
            return False
        
        # Skip if contains common date-related words
        date_words = ['date', 'published', 'updated', 'created', 'modified', 'sept', 'oct', 'nov', 'dec', 'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug']
        if any(word in name.lower() for word in date_words):
            return False
        
        # Skip if it's just a single letter or very short
        if len(name.strip()) <= 2:
            return False
        
        # Skip if it contains special characters that aren't typical in names
        if re.search(r'[^\w\s\-\.]', name):
            return False
        
        # Must contain at least one letter
        if not re.search(r'[a-zA-Z]', name):
            return False
        
        return True

    # Simple fallback: validate the cleaned text directly
    if is_valid_author_name(text):
        return text
    
    return None


def get_date(soup_obj):
    # 1. Check JSON-LD for datePublished
    ld_json_scripts = soup_obj.find_all("script", type="application/ld+json")
    for script in ld_json_scripts:
        try:
            data = json.loads(script.string)
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict) and "datePublished" in item:
                        return item["datePublished"]
            elif isinstance(data, dict) and "datePublished" in data:
                return data["datePublished"]
        except Exception:
            continue

    # 2. Check meta tag
    meta_date = soup_obj.find("meta", property="article:published_time")
    if meta_date and meta_date.get("content"):
        return meta_date["content"]

    # 3. Look for class names containing "date"
    date_candidates = soup_obj.find_all(class_=lambda c: c and "date" in c.lower())
    for tag in date_candidates:
        text = tag.get_text(strip=True)
        if text and any(char.isdigit() for char in text):
            return text

    return None

# Extract metadata 
def extract_article_metadata(url: str, idx=None) -> tuple[dict, dict]:
    """
    Extracts metadata from a web page.

    Args:
        url (str): The URL of the web page.
        idx (int, optional): Index for the CSL item. Defaults to None.

    Returns:
        tuple[dict, dict]: A tuple containing the extracted metadata and CSL JSON.
    """
    headers = {
        "User-Agent": "CiteAI/1.0"
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
    except requests.RequestException as e:
        print("Request failed:", e)
        return {}, {}

    try:
        soup = BeautifulSoup(response.text, "html.parser")

        meta = {
            "title": get_title(soup),
            "author": get_author(soup),
            "date":      get_date(soup),
            "url":       url,
            "site_name": getattr(soup.find("meta", property="og:site_name"), "content", None)
        }
        csl = build_csl_json(meta, idx)
        return meta, csl if csl else {}
    except Exception as e:
        print(f"Error extracting article metadata: {e}")
        return {}, {}