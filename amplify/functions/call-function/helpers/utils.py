import json
import re
import uuid
import math

from dateutil import parser as dateparser
from typing import Optional



"""
————————————————————————————————————
——————————————CONSTANTS————————————————
————————————————————————————————————
"""

# ---- Citation Calculation Tunables ----
PLAN_TOTAL_CAP   = {"free": 1, "plus": 20, "pro": 40}
PLAN_REQUEST_CAP = {"free": 1, "plus": 12, "pro": 16}
PLAN_MULT        = {"free": 1.0, "plus": 1.0, "pro": 1.2}
GLOBAL_MAX = 50

# --- regex helpers ---
URL_RE   = re.compile(r"\bhttps?://[^\s)]+", re.I)
DOI_RE   = re.compile(r"\b10\.\d{4,9}/\S+\b", re.I)
PAREN_CITE_RE = re.compile(r"\([A-Z][A-Za-z-]+(?:\s+et\s+al\.)?(?:,\s*\d{4}[a-z]?)\)", re.U)

TRIGGER_KEYWORDS = [
    # Statistical / Quantitative
    "percent", "%", "ratio", "rate", "majority", "average", "mean", "median",
    "proportion", "increase", "decrease", "growth", "decline",

    # Academic / Research Context
    "study", "studies", "research", "report", "survey", "analysis", "evidence",
    "experiment", "results", "data", "paper", "meta-analysis", "dataset",
    "trial", "theory", "literature", "framework", "model", "regression",
    "statistically", "significant", "odds ratio", "p-value", "methodology",
    "discussion", "limitations", "implications", "systematic", "review",

    # Attribution / Claims
    "according", "claims", "found", "suggests", "concludes", "indicates",
    "shows", "reported", "revealed", "published", "mentions", "responded",
    "findings",

    # Phrases
    "according to", "studies show", "research indicates", "data suggests",
]


"""
————————————————————————————————————
————————————Citation Calculation————————————
————————————————————————————————————
"""
def word_count_fast(text: str) -> int:
    return len(re.findall(r"\b\w+\b", text))

def _estimate_pages_from_words(total_words: int, spacing: str = "double") -> float:
    """500 wpp = single, 250 wpp = double (default)."""
    spacing = (spacing or "double").lower()
    wpp = 500 if spacing == "single" else 250
    return max(1.0, total_words / float(wpp))

def _research_intensity_score(text: str) -> float:
    """0..1 score from trigger words + year mentions."""
    t = text.lower()
    trigger_hits = sum(t.count(w) for w in TRIGGER_KEYWORDS)
    trigger = min(trigger_hits / 12.0, 1.0)
    year_hits = len(re.findall(r"\b(19[5-9]\d|20[0-2]\d)\b", t))
    years = min(year_hits / 10.0, 1.0)
    return max(0.0, min(0.65 * trigger + 0.35 * years, 1.0))

def _density_from_intensity(intensity: float) -> float:
    """Use band [1.6, 2.6] citations per double-spaced page, center ≈2.1/page."""
    return 1.6 + (2.6 - 1.6) * intensity

def _source_demand_signals(text: str, explicit_quote_count: Optional[int] = None) -> int:
    """Minimum number of distinct sources implied by quotes/URLs/parentheses."""
    if explicit_quote_count is not None:
        quotes = max(0, int(explicit_quote_count))
    else:
        quotes = text.count('"') // 2

    urls = set(URL_RE.findall(text))
    dois = set(DOI_RE.findall(text))
    paren_cites = len(PAREN_CITE_RE.findall(text))

    demand_score = len(urls) + len(dois) + paren_cites + math.ceil(0.6 * quotes)
    if (quotes > 0) or (len(urls) + len(dois) > 0) or (paren_cites > 0):
        demand_score = max(demand_score, 2)
    return demand_score

def compute_paper_target(plan: str,
                         *,
                         total_word_count: int,
                         spacing: str = "double",
                         intensity_hint: Optional[float] = None,
                         sample_text: Optional[str] = None) -> int:
    plan = (plan or "free").lower()
    if plan == "free":
        return 1

    pages = _estimate_pages_from_words(total_word_count, spacing)
    if intensity_hint is None:
        intensity = _research_intensity_score(sample_text or "")
        if sample_text is None:
            intensity = 0.4
    else:
        intensity = max(0.0, min(float(intensity_hint), 1.0))

    density = _density_from_intensity(intensity)
    base_total = pages * density
    scaled = math.ceil(base_total * PLAN_MULT.get(plan, 1.0))

    total_cap = PLAN_TOTAL_CAP.get(plan, 20 if plan == "plus" else 40)
    return int(min(scaled, total_cap, GLOBAL_MAX))

def num_citations(plan: str,
                  content_chunk: str,
                  *,
                  paper_target: Optional[int] = None,
                  total_word_count: Optional[int] = None,
                  spacing: str = "double",
                  already_cited: int = 0,
                  intensity_hint: Optional[float] = None,
                  explicit_quote_count: Optional[int] = None) -> int:
    """
    Decide how many citations to generate for THIS chunk.
    Uses max(length_target, demand_target) and clamps to remaining budget & plan caps.
    """
    plan = (plan or "free").lower()

    if plan == "free":
        return 1

    if paper_target is None and total_word_count is not None:
        paper_target = compute_paper_target(plan,
                                            total_word_count=total_word_count,
                                            spacing=spacing,
                                            intensity_hint=intensity_hint,
                                            sample_text=content_chunk)

    total_cap = PLAN_TOTAL_CAP.get(plan, 20 if plan == "plus" else 40)
    if paper_target is None:
        paper_target = total_cap

    remaining = max(0, paper_target - already_cited)
    if remaining == 0:
        return 0

    wc = word_count_fast(content_chunk)
    pages_local = _estimate_pages_from_words(wc, spacing)
    local_intensity = _research_intensity_score(content_chunk) if content_chunk else (intensity_hint or 0.4)
    density_local = _density_from_intensity(local_intensity)
    length_target = math.ceil(pages_local * density_local * PLAN_MULT.get(plan, 1.0))

    demand_target = _source_demand_signals(content_chunk, explicit_quote_count)

    proposed = max(length_target, demand_target)
    request_cap = PLAN_REQUEST_CAP.get(plan, 12)
    per_request = min(proposed, request_cap, remaining)

    return int(max(1 if wc > 0 and remaining > 0 else 0, per_request))



"""
———————————————————————————
————————CSL helper methods————————
———————————————————————————
"""

def build_csl_json(meta: dict, idx=None) -> dict | None:
    """
    Build CSL JSON from meta data
    """
    # Skip if no title (will cause "None" entries)
    if not meta or not meta.get("title"):
        return None
    csl = {
        "id": meta.get("id") or f"ref{idx or uuid.uuid4()}",
        "type": meta.get("type", "article-journal"),
        "title": meta.get("title"),
        "URL": meta.get("url")
    }

    max_authors = 5

    # Handle authors from OpenAlex/Crossref (list of strings or dicts)
    authors = meta.get("authors")
    if authors:
        csl_authors = []
        for i, a in enumerate(authors):
            is_last = (i == len(authors) - 1) or (i == max_authors - 1)
            if isinstance(a, dict) and a.get("name"):
                csl_authors.append(_parse_author_name(a["name"], is_last_author=is_last))
            elif isinstance(a, dict) and a.get("given") and a.get("family"):
                csl_authors.append({"given": a["given"], "family": a["family"]})
            elif isinstance(a, str):
                # Split string into given/family (best effort)
                parts = a.strip().split()
                if len(parts) == 1:
                    csl_authors.append({"family": parts[0]})
                elif len(parts) > 1:
                    csl_authors.append({"given": " ".join(parts[:-1]), "family": parts[-1]})
        csl["author"] = csl_authors[:max_authors]
    elif meta.get("author"):
        csl["author"] = [_parse_author_name(meta["author"], is_last_author=True)]

    # date / year
    date_val = meta.get("date") or meta.get("year")
    if date_val:
        s = str(date_val)
        if re.fullmatch(r"\d{4}", s):             # year only
            parts = [int(s)]
            csl["issued"] = {"date-parts": [parts]}
        else:
            cleaned_s = clean_date_string(s)
            try:
                dt = dateparser.parse(cleaned_s)
                parts = [dt.year]
                if dt.month:
                    parts.append(dt.month)
                if dt.day:
                    parts.append(dt.day)
                csl["issued"] = {"date-parts": [parts]}
            except Exception:
                pass

    # container title - always prioritize 'venue' if present
    if meta.get("venue"):
        csl["container-title"] = meta["venue"]
    elif meta.get("site_name"):
        csl["container-title"] = meta["site_name"]
    # Do not use DOI as container-title
    if meta.get("DOI"):
        csl["DOI"] = meta["DOI"]
    elif meta.get("externalIds") and isinstance(meta["externalIds"], dict):
        doi = meta["externalIds"].get("DOI")
        if doi:
            csl["DOI"] = doi

    return csl

def deduplicate_csl_items(csl_items):
    """
    Deduplicate CSL items based on DOI, URL, or full CSL content.
    """
    seen = set()
    deduped = []
    for csl in csl_items:
        key = csl.get("DOI") or csl.get("URL") or json.dumps(csl, sort_keys=True)
        if key not in seen:
            seen.add(key)
            deduped.append(csl)
    return deduped


"""
————————————————————————————
————————WEB SCRAPING TOOLS—————————
————————————————————————————
"""
class CitationCandidate:
    def __init__(self, sentence, needs_citation=False):
        self.sentence = sentence
        self.needs_citation = needs_citation

    def flag(self):
        self.needs_citation = True

def parse_json_input():
    import sys, json
    if not sys.stdin.isatty():
        data = sys.stdin.read()
        return json.loads(data)
    return None

def clean_date_string(s: str) -> str:
    # Remove prefixes like 'Date:', 'Published:', etc.
    s = re.sub(r"^(date|published|on)[:\-\s]+", "", s.strip(), flags=re.IGNORECASE)
    return s.strip()

def _parse_author_name(name: str, is_last_author=False) -> dict:
    # Remove "by" prefix if present
    name = re.sub(r"^by\s*", "", name, flags=re.IGNORECASE)
    name = re.sub(r"\s*[\|‐–-]\s*.*$", "", name)
    name = re.sub(r"\b(BBC|CNN|Times|News|Daily|Post)$", "", name).strip()
    parts = name.split()
    if len(parts) == 1:
        return {"family": parts[0]}
    
    # For the last author, don't add periods to initials
    given = " ".join(parts[:-1])
    if is_last_author:
        # Remove periods from initials for last author
        given = re.sub(r"\.", "", given)
    
    return {"given": given, "family": parts[-1]}



"""
————————————————————————————
——————————PARSE SOURSES——————————
————————————————————————————
"""

# Robustly parse all sources from Perplexity output
def parse_multiple_sources(output):
    # If output is already a list of dicts (expected JSON format)
    if isinstance(output, list):
        return output
    # If output is a dict with url/title/is_paper, wrap in a list
    if isinstance(output, dict) and output.get('url'):
        return [output]
    # If output is a dict with 'results' key
    if isinstance(output, dict) and 'results' in output and isinstance(output['results'], list):
        return output['results']
    # Fallback: if output is a string, try to parse as JSON
    if isinstance(output, str):
        try:
            parsed = json.loads(output)
            if isinstance(parsed, list):
                return parsed
            elif isinstance(parsed, dict) and parsed.get('url'):
                return [parsed]
        except json.JSONDecodeError:
            # If JSON parsing fails, try the old text parsing as fallback
            blocks = [b.strip() for b in re.split(r'\n\s*\n|(?=URL:)', output) if b.strip()]
            results = []
            for block in blocks:
                url = re.search(r'URL:\s*(.+)', block)
                title = re.search(r'TITLE:\s*(.+)', block)
                is_paper = re.search(r'Research paper:\s*(yes|no)', block, re.IGNORECASE)
                if url and title and is_paper:
                    results.append({
                        'url': url.group(1).strip(),
                        'title': title.group(1).strip(),
                        'is_paper': is_paper.group(1).strip().lower() == 'yes'
                    })
            return results
    return []