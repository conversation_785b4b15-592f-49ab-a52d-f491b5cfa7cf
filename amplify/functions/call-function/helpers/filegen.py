#!/usr/bin/env python3
import re
from urllib.parse import urlparse

from citeproc.source.json import CiteProcJSON
from citeproc import CitationStylesStyle, CitationStylesBibliography, Citation, CitationItem, formatter
from citeproc_styles import get_style_filepath

# 不在模块级别导入WeasyPrint，避免系统库依赖问题
WEASYPRINT_AVAILABLE = False

def extract_website_name(url):
    """Extract website name from URL to use as container title."""
    if not url:
        return "Online Publication"
    
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove www. prefix
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # Extract the main domain name
        parts = domain.split('.')
        if len(parts) >= 2:
            # Handle cases like "news.bbc.co.uk" -> "BBC News"
            if len(parts) >= 3 and parts[-2] in ['co', 'com', 'org', 'net']:
                main_name = parts[-3]
            else:
                main_name = parts[-2]
            
            # Clean up common domain suffixes
            main_name = main_name.replace('-', ' ').replace('_', ' ')
            
            # Capitalize properly
            words = main_name.split()
            capitalized_words = [word.capitalize() for word in words]
            
            return ' '.join(capitalized_words)
        
        return "Online Publication"
    except:
        return "Online Publication"

def format_author_names(author_list):
    """Format author names according to APA rules: Last, F. M."""
    if not author_list:
        return []
    
    formatted_authors = []
    for author in author_list:
        if not isinstance(author, dict):
            continue
            
        family = author.get("family", "")
        given = author.get("given", "")
        
        if not family:
            continue
            
        # Split given name into parts
        given_parts = given.split()
        initials = []
        
        for part in given_parts:
            if part:
                initials.append(part[0].upper() + ".")
        
        # Format as "Last, F. M."
        if initials:
            formatted_name = f"{family}, {' '.join(initials)}"
        else:
            formatted_name = family
            
        formatted_authors.append(formatted_name)
    
    return formatted_authors

def extract_website_name_from_url(url):
    """Extract website name (e.g., 'grammarly' from 'grammarly.com') and capitalize first letter for APA formatting when no author is present."""
    if not url:
        return "Unknown Website"
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove www. prefix
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # Extract just the main domain name (before the first dot)
        website_name = domain.split('.')[0]
        
        # Capitalize the first letter
        return website_name.capitalize()
    except:
        return "Unknown Website"

def sort_csl_items(csl_items, style=None):
    """Sort CSL items by the first author's family name (alphabetical), or by title if no author (for MLA)."""
    def get_sort_key(item):
        authors = item.get("author", [])
        if style and style.lower() == "mla":
            # MLA: If no author, sort by first significant word of the title
            if not authors or (len(authors) == 1 and not authors[0].get('family', '')):
                title = item.get("title", "").lower()
                # Remove leading quotes and articles
                title = re.sub(r'^[“”"\']+', '', title)
                title = re.sub(r'^(a|an|the)\s+', '', title)
                return title
        # Default: sort by first author's family name
        if isinstance(authors, list) and authors and authors[0].get("family", ""):
            return authors[0].get("family", "").lower()
        return ""
    return sorted(csl_items, key=get_sort_key)

def clean_author_names(csl_list):
    """Clean up malformed author names like 'byScott' -> 'Scott'."""
    for item in csl_list:
        if "author" in item and isinstance(item["author"], list):
            for author in item["author"]:
                if "given" in author:
                    # Remove "by" prefix from author names (case insensitive)
                    given = author["given"]
                    if given.lower().startswith("by"):
                        author["given"] = given[2:].strip()
                    # Remove other common prefixes
                    for prefix in ["By", "By ", "By:"]:
                        if given.startswith(prefix):
                            author["given"] = given[len(prefix):].strip()
    return csl_list

def fix_missing_fields(csl_list, style=None):
    """Ensure all required fields are present for citeproc-py. Only set website name as author for APA, not MLA."""
    for item in csl_list:
        if not isinstance(item, dict):
            continue
        # For APA format: when no author, use website name as author
        if ("author" not in item or not item["author"]):
            if style and style.lower() == "apa":
                url = item.get("URL", "")
                website_name = extract_website_name_from_url(url)
                item["author"] = [{"family": website_name, "given": ""}]
                # Remove container-title for website citations to avoid duplication
                if "container-title" in item:
                    del item["container-title"]
            # For MLA, leave author field empty so citeproc-py starts with the title
        # Ensure issued field exists
        if "issued" not in item or not item["issued"]:
            item["issued"] = {"date-parts": [[2024]]}
        # Ensure container-title exists (for articles) - only if not already present and not a website citation
        if item.get("type") == "article-journal" and "container-title" not in item:
            # Check if this is a website citation (no real author)
            has_website_author = False
            if "author" in item and item["author"]:
                author = item["author"][0].get("family", "")
                url = item.get("URL", "")
                has_website_author = author and url and author.lower() in url.lower()
            # Only add container-title if it's not a website citation
            if not has_website_author:
                url = item.get("URL", "")
                item["container-title"] = extract_website_name(url)
        if "URL" not in item:
            item["URL"] = ""
        if "type" not in item:
            item["type"] = "article-journal"
    return csl_list

def build_formatted_entries(csl_list, style):
    """
    Render a list of CSL-JSON dicts into formatted citation strings.
    Ensures each dict has an 'id', loads the proper .csl style via get_style_filepath,
    and feeds the list directly into CiteProcJSON.
    """
    # choose the CSL style key
    style_key = "apa" if style.lower() == "apa" else "modern-language-association"
    # load the actual .csl file
    style_path = get_style_filepath(style_key)
    cs_style = CitationStylesStyle(style_path, validate=False)

    # assign an 'id' to any record missing one
    for idx, item in enumerate(csl_list):
        if not isinstance(item, dict):
            continue
        item["id"] = f"ref{idx}"

    source = CiteProcJSON(csl_list)
    bib = CitationStylesBibliography(cs_style, source, formatter.plain)

    entries = []
    for item in csl_list:
        cid = item.get("id")
        cite = Citation([CitationItem(cid)])
        bib.register(cite)
    
    # Get all bibliography entries
    for entry in bib.bibliography():
        # Join the entry parts into a single string
        entry_text = ''.join(entry)
        entries.append(entry_text)
    
    return entries

def abbreviate_given_names(csl_list):
    """Abbreviate given names for APA style only."""
    for item in csl_list:
        if "author" in item and isinstance(item["author"], list):
            for author in item["author"]:
                given = author.get("given", "")
                if given:
                    parts = given.split()
                    initials = [p[0].upper() + "." for p in parts if p]
                    author["given"] = " ".join(initials)
    return csl_list

def format_mla_date(date_parts):
    """Format date-parts as '8 June 2015' for MLA."""
    import calendar
    if not date_parts or not isinstance(date_parts, list):
        return ""
    parts = date_parts[0]
    if len(parts) == 3:
        day, month, year = parts[2], parts[1], parts[0]
        month_name = calendar.month_name[month]
        return f"{day} {month_name} {year}"
    elif len(parts) == 2:
        month, year = parts[1], parts[0]
        month_name = calendar.month_name[month]
        return f"{month_name} {year}"
    elif len(parts) == 1:
        return str(parts[0])
    return ""

def capitalize_first_letter(text):
    if not text:
        return text
    return text[0].upper() + text[1:]

def format_mla_authors(authors):
    """Format author names for MLA: 1 author: Last, First. 2 authors: Last, First, and First Last. 3+: Last, First, et al."""
    if not authors or not isinstance(authors, list):
        return ""
    if len(authors) == 1:
        a = authors[0]
        return f"{a.get('family', '')}, {a.get('given', '')}"
    elif len(authors) == 2:
        a1, a2 = authors[0], authors[1]
        return f"{a1.get('family', '')}, {a1.get('given', '')}, and {a2.get('given', '')} {a2.get('family', '')}"
    else:
        a = authors[0]
        return f"{a.get('family', '')}, {a.get('given', '')}, et al."

def generate_html(formatted_entries, style, sorted_items):
    """Wrap entries in double-spaced HTML with a centered heading. MLA: full names, quoted titles, italicized container, formatted date, stripped URL, Accessed date."""
    import datetime
    title = "References" if style.lower() == "apa" else "Works Cited"
    css = """
    @page { 
        size: letter; 
        margin: 1in;
        @top-center { content: ""; }
        @bottom-center { content: ""; }
    }
    body {
        font-family: "Times New Roman", serif;
        font-size: 12pt;
        line-height: 2;
        margin: 0;
        color: black;
    }
    h1 {
        text-align: center;
        margin-bottom: 0.5em;
        line-height: 1.2;
        font-weight: bold;
        font-size: 12pt;
        text-transform: none;
    }
    .citation {
        text-indent: -0.5in;
        margin-left: 0.5in;
        margin-bottom: 0.2em;
        text-align: justify;
        hanging-punctuation: first;
        word-break: break-all;
        overflow-wrap: anywhere;
    }
    .italic-title {
        font-style: italic;
    }
    .quoted-title {
        font-style: normal;
    }
    """
    parts = [
        "<!DOCTYPE html>",
        "<html><head><meta charset='utf-8'>",
        f"<style>{css}</style></head><body>",
        f"<h1>{title}</h1>"
    ]
    processed_entries = []
    today = datetime.date.today().strftime("%d %B %Y")
    for entry, item in zip(formatted_entries, sorted_items):
        if style.lower() == "mla":
            authors = item.get("author", [])
            # If no real author, build the citation manually from citation.json fields
            no_real_author = not authors or (len(authors) == 1 and not authors[0].get('family', '') and not authors[0].get('given', ''))
            if no_real_author:
                art_title = item.get("title", "")
                clean_title = re.sub(r'^[“”"\']+|[“”"\']+$', '', art_title)
                quoted = f'<span class="quoted-title">“{clean_title}."</span>'
                cont_title = item.get("container-title", "")
                cont_title_html = f'<span class="italic-title">{cont_title}</span>' if cont_title else ''
                issued = item.get("issued", {}).get("date-parts", [])
                mla_date = format_mla_date(issued)
                url = item.get("URL", "")
                url_stripped = re.sub(r'^https?://', '', url) if url else ''
                accessed = f'Accessed {today}.'
                # Compose the citation
                entry = f'{quoted} {cont_title_html}, {mla_date}, {url_stripped}. {accessed}'
                entry = re.sub(r'\s+,', ',', entry)
                entry = re.sub(r',\s+,', ', ', entry)
                entry = re.sub(r'\s{2,}', ' ', entry)
                entry = entry.replace('..', '.')
                processed_entries.append(entry)
                continue
            # MLA: Format author names
            is_website_author = False
            if len(authors) == 1 and authors[0].get('given', '') == '' and authors[0].get('family', '') and item.get('container-title', ''):
                if authors[0]['family'].lower() == item['container-title'].replace(' ', '').lower():
                    is_website_author = True
            if is_website_author:
                # Manually construct the citation: "Title." <i>Container</i>, Date, URL. Accessed ...
                art_title = item.get("title", "")
                clean_title = re.sub(r'^[“”"\']+|[“”"\']+$', '', art_title)
                quoted = f'<span class="quoted-title">“{clean_title}."</span>'
                cont_title = item.get("container-title", "")
                if cont_title:
                    cont_title_html = f'<span class="italic-title">{cont_title}</span>'
                else:
                    cont_title_html = ''
                issued = item.get("issued", {}).get("date-parts", [])
                mla_date = format_mla_date(issued)
                url = item.get("URL", "")
                url_stripped = re.sub(r'^https?://', '', url) if url else ''
                accessed = f'Accessed {today}.'
                # Compose the citation
                entry = f'{quoted} {cont_title_html}, {mla_date}, {url_stripped}. {accessed}'
                # Remove any duplicate spaces or stray punctuation
                entry = re.sub(r'\s+,', ',', entry)
                entry = re.sub(r',\s+,', ', ', entry)
                entry = re.sub(r'\s{2,}', ' ', entry)
                entry = entry.replace('..', '.')
            else:
                mla_authors = format_mla_authors(authors)
                entry = re.sub(r'^.*?\.', mla_authors + '.', entry, count=1)
                # Remove any repeated author/initials pattern after the author list
                entry = re.sub(rf'({re.escape(mla_authors)}\.)\s+\1', r'\1', entry)
                # Remove any stray initials/author after the author list and before the quoted title
                entry = re.sub(r'([A-Z]\.and\s+[A-Z]\.\s+[A-Z][a-z]+\.)\s*(?=“)', '', entry)
            # Remove duplicate 'et al.' if present
            entry = re.sub(r'(et al\.)[.,\s]*(et al\.)+', r'et al.', entry)
            # Remove double periods after author names (e.g., 'Jill..' -> 'Jill.')
            entry = re.sub(r'([A-Za-z])\.\.', r'\1.', entry)
            # Article title in quotes
            art_title = item.get("title", "")
            if art_title:
                clean_title = re.sub(r'^[“”"\']+|[“”"\']+$', '', art_title)
                quoted = f'<span class="quoted-title">“{clean_title}"</span>'
                entry = re.sub(r'[“”"\']*' + re.escape(clean_title) + r'[“”"\']*', quoted, entry, count=1)
                entry = re.sub(r'([“”]){2,}', r'\1', entry)
                # Remove any period after the closing quote/span
                entry = re.sub(r'("</span>)[.]', r'"</span>', entry)
                # If the quoted title does not end with a period before the quote, insert one
                entry = re.sub(r'([^\.])"</span>', r'\1."</span>', entry)
                # Remove any double periods before the closing quote/span
                entry = re.sub(r'\.\."</span>', r'."</span>', entry)
            # Italicize container-title
            cont_title = item.get("container-title", "")
            if cont_title:
                entry = re.sub(re.escape(cont_title), f'<span class="italic-title">{cont_title}</span>', entry, count=1)
            # Format date
            issued = item.get("issued", {}).get("date-parts", [])
            mla_date = format_mla_date(issued)
            if mla_date:
                entry = re.sub(r'\b\d{4}\b', mla_date, entry, count=1)
                entry = re.sub(
                    r'((Jan\.?|Feb\.?|Mar\.?|Apr\.?|May|Jun\.?|Jul\.?|Aug\.?|Sep\.?|Sept\.?|Oct\.?|Nov\.?|Dec\.?|January|February|March|April|June|July|August|September|October|November|December)\s*)?(\d{1,2}\s*)?(\d{4}\s*)?'
                    + re.escape(mla_date),
                    mla_date,
                    entry,
                    count=1
                )
                entry = re.sub(
                    re.escape(mla_date) +
                    r'[.,]?\s*((Jan\.?|Feb\.?|Mar\.?|Apr\.?|May|Jun\.?|Jul\.?|Aug\.?|Sep\.?|Sept\.?|Oct\.?|Nov\.?|Dec\.?|January|February|March|April|June|July|August|September|October|November|December)\s*)?(\d{1,2}\s*)?(\d{4}\s*)?',
                    mla_date,
                    entry,
                    count=1
                )
                entry = re.sub(rf'({re.escape(mla_date)})((?=[^,]))', r'\1, ', entry, count=1)
            # Strip protocol from URL
            url = item.get("URL", "")
            if url:
                url_stripped = re.sub(r'^https?://', '', url)
                entry = entry.replace(url, url_stripped)
            # Add Accessed date if not present
            if "Accessed" not in entry:
                entry = entry.rstrip('.') + f'. Accessed {today}.'
            # FINAL: Ensure period is inside the closing quote for MLA titles (post-processing)
            entry = re.sub(r'("</span>)\.', r'."</span>', entry)
            # FINAL: If website-as-author, remove everything up to and including the first period and any following punctuation/space
            if is_website_author:
                entry = re.sub(r'^.*?\.\s*', '', entry, count=1)
                entry = re.sub(r'^[.,\s]+', '', entry)
            # FINAL: Move the closing quote and span after the period following the title for MLA
            entry = re.sub(r'(</span>)\s*\.', r'.</span>', entry)
        else:
            # APA: Italicize all article titles
            title = item.get("title", "")
            if title:
                cap_title = capitalize_first_letter(title)
                entry = re.sub(re.escape(title), cap_title, entry, count=1)
                entry = re.sub(re.escape(cap_title), f'<span class="italic-title">{cap_title}</span>', entry, count=1)
            # Fix double periods in author names (e.g., "Nichols, L.." -> "Nichols, L.")
            entry = re.sub(r'([A-Z][a-z]+,\s+[A-Z])\.\.', r'\1.', entry)
            entry = re.sub(r'([A-Z][a-z]+,\s+[A-Z]\s+[A-Z])\.\.', r'\1.', entry)
            entry = re.sub(r'([A-Z][a-z]+,\s+[A-Z]\.\s+[A-Z])\.\.', r'\1.', entry)
            entry = re.sub(r'([A-Z][a-z]+,\s+[A-Z]\.\s*&\s*[A-Z][a-z]+,\s+[A-Z])\.\.', r'\1.', entry)
            entry = re.sub(r'([A-Z][a-z]+,\s+[A-Z]\s+[A-Z]\.\s*&\s*[A-Z][a-z]+,\s+[A-Z])\.\.', r'\1.', entry)
            entry = re.sub(r'^([A-Z][a-z]+)\.\.', r'\1.', entry)
            entry = re.sub(r'^([A-Z][a-z]+\s+[A-Z][a-z]+)\.\.', r'\1.', entry)
            entry = re.sub(r'([A-Z][a-z]+),\s*\.\.', r'\1.', entry)
            entry = re.sub(r'([A-Z][a-z]+\s+[A-Z][a-z]+),\s*\.\.', r'\1.', entry)
        processed_entries.append(entry)
    # FINAL MLA post-processing: move period after quote to inside the quote for all entries
    if style.lower() == "mla":
        processed_entries_fixed = []
        for entry in processed_entries:
            entry = re.sub(r'("</span>)\.', r'."</span>', entry)
            entry = re.sub(r'"\.', '."', entry)
            processed_entries_fixed.append(entry)
        parts += [f'<div style="text-indent: -0.5in; margin-left: 0.5in; margin-bottom: 0.2em; text-align: justify; word-break: break-all; overflow-wrap: anywhere;">{e}</div>' for e in processed_entries_fixed]
    else:
        parts += [f'<div style="text-indent: -0.5in; margin-left: 0.5in; margin-bottom: 0.2em; text-align: justify; word-break: break-all; overflow-wrap: anywhere;">{e}</div>' for e in processed_entries]
    parts.append("</body></html>")
    return "\n".join(parts)

def render_pdf(html_str, output_path):
    """Generate a PDF file from HTML via WeasyPrint."""
    try:
        from weasyprint import HTML
        HTML(string=html_str).write_pdf(output_path)
        print(f"✅ PDF successfully written to: {output_path}")
    except ImportError as import_error:
        raise ImportError(f"WeasyPrint is not available: {import_error}. PDF generation is disabled to reduce Lambda package size.")
    except Exception as weasy_error:
        raise RuntimeError(f"PDF generation failed: {weasy_error}")

def format_citations_text(csl_items, style):
    """
    Returns a string of formatted citations (plain text, with indentation and line breaks) for the given style (apa or mla).
    """
    # Clean and sort items as in the main block
    valid_items = [item for item in csl_items if isinstance(item, dict) and item.get("title")]
    valid_items = fix_missing_fields(valid_items, style)
    valid_items = clean_author_names(valid_items)
    if style.lower() == "apa":
        valid_items = abbreviate_given_names(valid_items)
    sorted_items = sort_csl_items(valid_items, style)
    entries = build_formatted_entries(sorted_items, style)
    # Indent second and subsequent lines (hanging indent)
    def hanging_indent(text, indent=4):
        lines = text.splitlines() or [text]
        if not lines:
            return ""
        result = lines[0]
        for line in lines[1:]:
            result += "\n" + ("\t" * (indent // 4)) + line.lstrip()
        return result
    # Join entries with double line breaks, apply hanging indent
    formatted = "\n\n".join([hanging_indent(e) for e in entries])
    # Add heading
    heading = "References" if style.lower() == "apa" else "Works Cited"
    return f"{heading}\n\n{formatted}\n"

def generate_citations_pdf(csl_items, style, output_path="output.pdf"):
    """
    Generates a PDF of the citations in the given style and returns the file path.
    """
    valid_items = [item for item in csl_items if isinstance(item, dict) and item.get("title")]
    valid_items = fix_missing_fields(valid_items, style)
    valid_items = clean_author_names(valid_items)
    if style.lower() == "apa":
        valid_items = abbreviate_given_names(valid_items)
    sorted_items = sort_csl_items(valid_items, style)
    entries = build_formatted_entries(sorted_items, style)
    html = generate_html(entries, style, sorted_items)
    render_pdf(html, output_path)
    return output_path