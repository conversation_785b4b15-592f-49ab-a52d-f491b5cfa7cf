import { defineFunction } from '@aws-amplify/backend';
import { Duration } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { PolicyStatement, Effect } from 'aws-cdk-lib/aws-iam';
import { Function, Runtime, Code, LayerVersion } from 'aws-cdk-lib/aws-lambda';
import path from 'path';
import { fileURLToPath } from 'url';

const NLTK_LAYER_ARN = 'arn:aws:lambda:us-east-2:271134112239:layer:citeai-python-dependency:7';
const S3_BUCKET_NAME = 'citeai-pdf-uploads';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const pdfProcessorFunction = defineFunction((scope: Construct) => {
  const nltkLayer = LayerVersion.fromLayerVersionArn(scope, 'NLTKLayerPdfProcessor', NLTK_LAYER_ARN);

  const func = new Function(scope, 'PdfProcessorFunction', {
    functionName: 'PdfProcessorFunction',
    runtime: Runtime.PYTHON_3_12,
    handler: 'index.handler',
    code: Code.fromAsset(path.join(__dirname)),
    memorySize: 512, // PDF处理需要更多内存
    timeout: Duration.seconds(30), // PDF处理可能需要更长时间
    environment: {
      S3_BUCKET_NAME,
    },
    layers: [nltkLayer],
  });

  // 添加S3权限用于上传PDF文件
  func.addToRolePolicy(new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ['s3:PutObject', 's3:GetObject', 's3:DeleteObject'],
    resources: [`arn:aws:s3:::${S3_BUCKET_NAME}/*`],
  }));

  func.addToRolePolicy(new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ['s3:ListBucket'],
    resources: [`arn:aws:s3:::${S3_BUCKET_NAME}`],
  }));

  return func;
});
