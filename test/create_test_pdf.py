#!/usr/bin/env python3
"""
创建一个简单的测试PDF文件用于测试PDF处理功能
"""

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    import os
    
    def create_test_pdf():
        """创建一个包含测试文本的PDF文件"""
        filename = "test_document.pdf"
        filepath = os.path.join(os.path.dirname(__file__), filename)
        
        # 创建PDF
        c = canvas.Canvas(filepath, pagesize=letter)
        width, height = letter
        
        # 添加标题
        c.setFont("Helvetica-Bold", 16)
        c.drawString(50, height - 50, "Test Document for PDF Processing")
        
        # 添加内容
        c.setFont("Helvetica", 12)
        y_position = height - 100
        
        content = [
            "This is a test document created for testing the PDF processing functionality.",
            "",
            "The document contains several paragraphs of text that should be extracted",
            "by the PDF processing Lambda function.",
            "",
            "Key features to test:",
            "1. Text extraction from PDF files",
            "2. Word count calculation",
            "3. Page count detection",
            "4. Proper formatting preservation",
            "",
            "This document should be processed successfully and the text should be",
            "extracted and displayed in the application interface.",
            "",
            "Additional test content:",
            "- Special characters: áéíóú, ñ, ü",
            "- Numbers: 123, 456.789",
            "- Symbols: @#$%^&*()",
            "",
            "End of test document."
        ]
        
        for line in content:
            c.drawString(50, y_position, line)
            y_position -= 20
            
            # 如果接近页面底部，创建新页面
            if y_position < 50:
                c.showPage()
                c.setFont("Helvetica", 12)
                y_position = height - 50
        
        # 保存PDF
        c.save()
        
        print(f"✅ Test PDF created: {filepath}")
        print(f"📄 File size: {os.path.getsize(filepath)} bytes")
        return filepath
    
    if __name__ == "__main__":
        create_test_pdf()
        
except ImportError:
    print("❌ reportlab library not installed.")
    print("💡 Install with: pip install reportlab")
    print("🔧 Alternative: Create a PDF manually using any PDF creator")
    
    # 创建一个简单的文本文件作为替代
    filename = "test_document.txt"
    filepath = os.path.join(os.path.dirname(__file__), filename)
    
    content = """Test Document for PDF Processing

This is a test document created for testing the PDF processing functionality.

The document contains several paragraphs of text that should be extracted
by the PDF processing Lambda function.

Key features to test:
1. Text extraction from PDF files
2. Word count calculation
3. Page count detection
4. Proper formatting preservation

This document should be processed successfully and the text should be
extracted and displayed in the application interface.

Additional test content:
- Special characters: áéíóú, ñ, ü
- Numbers: 123, 456.789
- Symbols: @#$%^&*()

End of test document."""
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Test text file created: {filepath}")
    print("📝 Convert this to PDF using any PDF creator for testing")
