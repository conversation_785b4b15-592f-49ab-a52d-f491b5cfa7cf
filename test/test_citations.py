#!/usr/bin/env python3

import sys
import re
import math
from typing import Optional

# Import the citation calculation functions from main.py
try:
    from main import (
        TRIGGER_KEYWORDS, 
        _word_count_fast, 
        _research_intensity_score, 
        _density_from_intensity,
        _source_demand_signals,
        compute_paper_target,
        num_citations
    )
except ImportError:
    print("Error: Could not import functions from main.py")
    print("Make sure you're running this from the same directory as main.py")
    sys.exit(1)

def test_citation_calculation():
    """Test function to see citation calculation output for different content types"""
    
    # Get user input for testing
    print("\n" + "="*60)
    print("CITATION CALCULATION TESTER")
    print("="*60)
    
    # Ask for plan
    plan = input("\nEnter plan (free/plus/pro): ").lower().strip()
    if plan not in ['free', 'plus', 'pro']:
        plan = 'free'
        print("Invalid plan, using 'free'")
    
    # Ask for content
    print("\nEnter your essay content (press Enter twice when done):")
    lines = []
    while True:
        line = input()
        if line == "" and lines and lines[-1] == "":
            break
        lines.append(line)
    
    content = "\n".join(lines[:-1])  # Remove the last empty line
    
    if not content.strip():
        print("No content provided, using sample content")
        content = "According to recent studies, the statistical analysis shows significant results."
    
    print("\n" + "="*60)
    print("CITATION CALCULATION RESULTS")
    print("="*60)
    
    print(f"\nPlan: {plan}")
    print(f"Content: {content[:100]}{'...' if len(content) > 100 else ''}")
    
    # Calculate word count
    word_count = _word_count_fast(content)
    print(f"Word count: {word_count}")
    
    # Calculate research intensity
    intensity = _research_intensity_score(content)
    print(f"Research intensity: {intensity:.3f}")
    
    # Calculate paper target
    paper_target = compute_paper_target(plan, total_word_count=word_count, sample_text=content)
    print(f"Paper target: {paper_target}")
    
    # Calculate citations for this chunk
    citations = num_citations(plan, content, 
                            paper_target=paper_target, 
                            total_word_count=word_count)
    print(f"Citations to generate: {citations}")
    
    # Show demand signals
    demand = _source_demand_signals(content)
    print(f"Demand signals: {demand}")
    
    # Show trigger words found
    found_triggers = [word for word in TRIGGER_KEYWORDS if word.lower() in content.lower()]
    print(f"Trigger words found: {found_triggers}")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    test_citation_calculation()