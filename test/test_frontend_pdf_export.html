<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Export Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🧪 CiteAI PDF Export Test</h1>
    <p>This page tests the PDF export functionality for Plus and Pro users.</p>

    <div class="test-section">
        <h2>📋 Test Data</h2>
        <p>Using sample citations for testing:</p>
        <ul>
            <li><strong>Article:</strong> "The Impact of AI on Academic Writing" by Smith & Doe (2024)</li>
            <li><strong>Website:</strong> "How to Write Better Citations" by Johnson (2024)</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔬 PDF Export Tests</h2>
        
        <h3>Test 1: MLA Format</h3>
        <button onclick="testPdfExport('mla')" id="mla-btn">Test MLA PDF Export</button>
        <div id="mla-result" class="result" style="display: none;"></div>

        <h3>Test 2: APA Format</h3>
        <button onclick="testPdfExport('apa')" id="apa-btn">Test APA PDF Export</button>
        <div id="apa-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📊 Test Results Summary</h2>
        <div id="summary" style="display: none;">
            <p><strong>MLA Test:</strong> <span id="mla-status">Not run</span></p>
            <p><strong>APA Test:</strong> <span id="apa-status">Not run</span></p>
        </div>
    </div>

    <script>
        // Sample test data - same as used in the Python test
        const testCslItems = [
            {
                "id": "test1",
                "type": "article-journal",
                "title": "The Impact of AI on Academic Writing",
                "author": [
                    {"family": "Smith", "given": "John"},
                    {"family": "Doe", "given": "Jane"}
                ],
                "container-title": "Journal of Educational Technology",
                "issued": {"date-parts": [[2024, 3, 15]]},
                "URL": "https://example.com/article1",
                "volume": "12",
                "issue": "3",
                "page": "45-67"
            },
            {
                "id": "test2", 
                "type": "webpage",
                "title": "How to Write Better Citations",
                "author": [
                    {"family": "Johnson", "given": "Alice"}
                ],
                "container-title": "Academic Writing Hub",
                "issued": {"date-parts": [[2024, 1, 20]]},
                "URL": "https://academicwriting.com/citations"
            }
        ];

        async function testPdfExport(format) {
            const btnId = `${format}-btn`;
            const resultId = `${format}-result`;
            const statusId = `${format}-status`;
            
            const button = document.getElementById(btnId);
            const resultDiv = document.getElementById(resultId);
            const statusSpan = document.getElementById(statusId);
            
            // Show loading state
            button.disabled = true;
            button.textContent = `Testing ${format.toUpperCase()}...`;
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.innerHTML = `🔄 Testing ${format.toUpperCase()} PDF export...`;
            
            try {
                console.log(`Testing ${format} PDF export...`);
                
                const response = await fetch('/api/citations/export-pdf', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        documentId: 'test-document-id',
                        citationFormat: format
                    })
                });

                console.log(`Response status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('Response data:', data);
                    
                    if (data.pdf_url) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            ✅ ${format.toUpperCase()} PDF generated successfully!<br>
                            📥 <a href="${data.pdf_url}" target="_blank" download="test-citations-${format}.pdf">Download PDF</a><br>
                            <small>URL: ${data.pdf_url.substring(0, 80)}...</small>
                        `;
                        statusSpan.textContent = '✅ PASS';
                        statusSpan.style.color = '#155724';
                    } else {
                        throw new Error('No PDF URL in response');
                    }
                } else {
                    const errorData = await response.json();
                    throw new Error(`HTTP ${response.status}: ${errorData.error || 'Unknown error'}`);
                }
                
            } catch (error) {
                console.error(`Error testing ${format}:`, error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ ${format.toUpperCase()} PDF export failed:<br><strong>${error.message}</strong>`;
                statusSpan.textContent = '❌ FAIL';
                statusSpan.style.color = '#721c24';
            } finally {
                // Reset button
                button.disabled = false;
                button.textContent = `Test ${format.toUpperCase()} PDF Export`;
                
                // Show summary
                document.getElementById('summary').style.display = 'block';
            }
        }

        // Add some helpful information
        console.log('PDF Export Test Page Loaded');
        console.log('Test data:', testCslItems);
        console.log('Make sure the development server is running on localhost:3000');
    </script>
</body>
</html>
