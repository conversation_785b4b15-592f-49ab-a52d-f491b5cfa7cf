#!/usr/bin/env python3
"""
Test script for PDF export functionality
Tests the PDF generation Lambda function directly
"""

import json
import requests
import sys
import os

# Add the project root to the path to import amplify outputs
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def load_amplify_outputs():
    """Load the amplify outputs to get function URLs"""
    try:
        with open('../amplify_outputs.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ amplify_outputs.json not found. Make sure Amplify is deployed.")
        return None

def test_pdf_generation():
    """Test the PDF generation Lambda function"""
    print("🧪 Testing PDF Export Functionality...")
    
    # Load amplify outputs
    outputs = load_amplify_outputs()
    if not outputs:
        return False
    
    pdf_generator_url = outputs.get('custom', {}).get('pdfGeneratorUrl')
    if not pdf_generator_url:
        print("❌ PDF generator URL not found in amplify outputs")
        return False
    
    print(f"📡 Using PDF generator URL: {pdf_generator_url[:50]}...")
    
    # Sample CSL data for testing
    test_csl_items = [
        {
            "id": "test1",
            "type": "article-journal",
            "title": "The Impact of AI on Academic Writing",
            "author": [
                {"family": "Smith", "given": "John"},
                {"family": "Doe", "given": "Jane"}
            ],
            "container-title": "Journal of Educational Technology",
            "issued": {"date-parts": [[2024, 3, 15]]},
            "URL": "https://example.com/article1",
            "volume": "12",
            "issue": "3",
            "page": "45-67"
        },
        {
            "id": "test2", 
            "type": "webpage",
            "title": "How to Write Better Citations",
            "author": [
                {"family": "Johnson", "given": "Alice"}
            ],
            "container-title": "Academic Writing Hub",
            "issued": {"date-parts": [[2024, 1, 20]]},
            "URL": "https://academicwriting.com/citations"
        }
    ]
    
    # Test both MLA and APA formats
    for citation_format in ['mla', 'apa']:
        print(f"\n📝 Testing {citation_format.upper()} format...")
        
        payload = {
            "csl_items": test_csl_items,
            "citation_format": citation_format
        }
        
        try:
            response = requests.post(
                pdf_generator_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=60
            )
            
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if 'download_url' in result:
                    print(f"✅ {citation_format.upper()} PDF generated successfully!")
                    print(f"📥 Download URL: {result['download_url'][:80]}...")
                    
                    # Test if the download URL is accessible
                    download_response = requests.head(result['download_url'])
                    if download_response.status_code == 200:
                        print(f"✅ Download URL is accessible")
                    else:
                        print(f"⚠️  Download URL returned status: {download_response.status_code}")
                else:
                    print(f"❌ No download URL in response: {result}")
                    return False
            else:
                print(f"❌ PDF generation failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            print(f"❌ Request timed out for {citation_format}")
            return False
        except Exception as e:
            print(f"❌ Error testing {citation_format}: {str(e)}")
            return False
    
    return True

def test_api_endpoint():
    """Test the Next.js API endpoint for PDF export"""
    print("\n🌐 Testing Next.js API endpoint...")
    
    # This would require a valid document ID and authentication
    # For now, just test that the endpoint exists
    try:
        response = requests.post(
            'http://localhost:3000/api/citations/export-pdf',
            json={'documentId': 'test-id', 'citationFormat': 'mla'},
            timeout=10
        )
        
        # We expect this to fail with authentication error, not 404
        if response.status_code in [400, 401, 403, 500]:
            print("✅ API endpoint exists and responds")
            return True
        elif response.status_code == 404:
            print("❌ API endpoint not found")
            return False
        else:
            print(f"📊 API endpoint returned: {response.status_code}")
            return True
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to local development server")
        print("💡 Make sure to run 'npm run dev' first")
        return False
    except Exception as e:
        print(f"❌ Error testing API endpoint: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 CiteAI PDF Export Test Suite")
    print("=" * 50)
    
    # Test Lambda function directly
    lambda_success = test_pdf_generation()
    
    # Test API endpoint
    api_success = test_api_endpoint()
    
    print("\n" + "=" * 50)
    print("📋 Test Results:")
    print(f"Lambda Function: {'✅ PASS' if lambda_success else '❌ FAIL'}")
    print(f"API Endpoint: {'✅ PASS' if api_success else '❌ FAIL'}")
    
    if lambda_success and api_success:
        print("\n🎉 All tests passed! PDF export functionality is working.")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        sys.exit(1)
