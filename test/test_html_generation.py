#!/usr/bin/env python3

import sys
import os
import json

# Add the parent directory to the path so we can import from the amplify function
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'amplify', 'functions', 'call-function'))

def test_html_generation():
    """Test HTML generation with the fixed code"""
    
    # Sample CSL item from the logs
    csl_items = [
        {
            "id": "ref85bc0d33-b2ab-4b2e-b575-f23d11c1207a",
            "type": "article-journal",
            "title": "#Activism: Identity, Affiliation, and Political Discourse-Making on Twitter",
            "URL": "https://doi.org/10.18357/ar.konnellya.612015",
            "author": [
                {
                    "given": "Alexah",
                    "family": "Konnelly"
                }
            ],
            "issued": {
                "date-parts": [
                    [2015]
                ]
            },
            "container-title": "The Arbutus Review"
        }
    ]
    
    print("=== Testing HTML generation ===")
    print(f"CSL items: {json.dumps(csl_items, indent=2)}")
    
    try:
        # Test the generate_output function
        from index import generate_output
        
        # Test HTML mode
        print("\n--- Testing HTML mode ---")
        result_html = generate_output(csl_items, "mla", "html")

        print(f"HTML Success! Result type: {type(result_html)}")
        print(f"HTML Result keys: {result_html.keys() if isinstance(result_html, dict) else 'Not a dict'}")

        if isinstance(result_html, dict) and "data" in result_html:
            html_data = result_html["data"]
            print(f"HTML data type: {type(html_data)}")
            print(f"HTML data length: {len(html_data) if isinstance(html_data, str) else 'Not a string'}")
            print(f"HTML preview: {html_data[:200] if isinstance(html_data, str) else html_data}...")

        # Test TEXT mode
        print("\n--- Testing TEXT mode ---")
        result_text = generate_output(csl_items, "mla", "text")

        print(f"TEXT Success! Result type: {type(result_text)}")
        print(f"TEXT Result keys: {result_text.keys() if isinstance(result_text, dict) else 'Not a dict'}")

        if isinstance(result_text, dict) and "data" in result_text:
            text_data = result_text["data"]
            print(f"TEXT data type: {type(text_data)}")
            print(f"TEXT data length: {len(text_data) if isinstance(text_data, str) else 'Not a string'}")
            print(f"TEXT preview: {text_data[:200] if isinstance(text_data, str) else text_data}...")

        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_html_generation()
    sys.exit(0 if success else 1)
