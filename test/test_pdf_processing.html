<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Processing Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: border-color 0.3s;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .extracted-text {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🧪 CiteAI PDF Processing Test</h1>
    <p>This page tests the PDF document processing functionality for Pro users.</p>

    <div class="test-section">
        <h2>📄 PDF Upload Test</h2>
        <p>Upload a PDF file to test the text extraction functionality.</p>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Drag and drop a PDF file here, or click to select</p>
            <input type="file" id="fileInput" accept=".pdf" style="display: none;">
            <button onclick="document.getElementById('fileInput').click()">Choose PDF File</button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
        <div id="extractedText" class="extracted-text" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🔧 Test with Sample PDF</h2>
        <p>Create a simple PDF for testing:</p>
        <button onclick="createSamplePdf()">Create Sample PDF</button>
        <div id="sampleResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const resultDiv = document.getElementById('result');
        const extractedTextDiv = document.getElementById('extractedText');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = Array.from(e.dataTransfer.files);
            const pdfFile = files.find(file => file.type === 'application/pdf');
            
            if (pdfFile) {
                processPdf(pdfFile);
            } else {
                showResult('error', '❌ Please drop a PDF file.');
            }
        });

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file && file.type === 'application/pdf') {
                processPdf(file);
            } else {
                showResult('error', '❌ Please select a PDF file.');
            }
        });

        async function processPdf(file) {
            showResult('loading', `🔄 Processing ${file.name}...`);
            extractedTextDiv.style.display = 'none';

            try {
                const formData = new FormData();
                formData.append('pdf', file);
                formData.append('documentId', 'test-document-id');

                console.log(`Processing PDF: ${file.name}, Size: ${(file.size / 1024 / 1024).toFixed(2)}MB`);

                const response = await fetch('/api/documents/process-pdf', {
                    method: 'POST',
                    body: formData,
                });

                console.log(`Response status: ${response.status}`);

                if (response.ok) {
                    const data = await response.json();
                    console.log('Processing result:', data);

                    showResult('success', `
                        ✅ PDF processed successfully!<br>
                        📄 <strong>File:</strong> ${file.name}<br>
                        📊 <strong>Pages:</strong> ${data.pages || 'Unknown'}<br>
                        📝 <strong>Words:</strong> ${data.wordCount || 0}<br>
                        💾 <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)}MB<br>
                        🔧 <strong>Method:</strong> ${data.extractionMethod || 'Unknown'}<br>
                        ${data.extractionError ? `⚠️ <strong>Warning:</strong> ${data.extractionError}<br>` : ''}
                        <strong>Message:</strong> ${data.message}
                    `);

                    if (data.extractedText) {
                        extractedTextDiv.style.display = 'block';
                        extractedTextDiv.textContent = data.extractedText;
                    }
                } else {
                    const errorData = await response.json();
                    showResult('error', `❌ Processing failed: ${errorData.error || 'Unknown error'}`);
                }

            } catch (error) {
                console.error('Error processing PDF:', error);
                showResult('error', `❌ Error: ${error.message}`);
            }
        }

        function showResult(type, message) {
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
        }

        // Create a simple PDF for testing
        async function createSamplePdf() {
            const sampleResultDiv = document.getElementById('sampleResult');
            sampleResultDiv.style.display = 'block';
            sampleResultDiv.className = 'result loading';
            sampleResultDiv.innerHTML = '🔄 Creating sample PDF...';

            try {
                // Create a simple PDF using jsPDF (if available) or provide instructions
                if (typeof window.jsPDF !== 'undefined') {
                    const { jsPDF } = window.jsPDF;
                    const doc = new jsPDF();
                    
                    doc.text('Sample PDF Document', 20, 20);
                    doc.text('This is a test document for PDF processing.', 20, 40);
                    doc.text('It contains some sample text that should be extracted.', 20, 60);
                    doc.text('Line 1: Introduction to the topic', 20, 80);
                    doc.text('Line 2: Main content and analysis', 20, 100);
                    doc.text('Line 3: Conclusion and recommendations', 20, 120);
                    
                    const pdfBlob = doc.output('blob');
                    const file = new File([pdfBlob], 'sample-test.pdf', { type: 'application/pdf' });
                    
                    sampleResultDiv.className = 'result success';
                    sampleResultDiv.innerHTML = '✅ Sample PDF created! Processing...';
                    
                    // Process the created PDF
                    await processPdf(file);
                } else {
                    sampleResultDiv.className = 'result error';
                    sampleResultDiv.innerHTML = `
                        ❌ jsPDF library not loaded.<br>
                        To test with a sample PDF:<br>
                        1. Create a simple PDF file with some text<br>
                        2. Use the upload area above to test it<br>
                        3. Or add jsPDF library to this page
                    `;
                }
            } catch (error) {
                sampleResultDiv.className = 'result error';
                sampleResultDiv.innerHTML = `❌ Error creating sample PDF: ${error.message}`;
            }
        }

        // Add some helpful information
        console.log('PDF Processing Test Page Loaded');
        console.log('Make sure the development server is running on localhost:3000');
        console.log('This test requires Pro user permissions');
    </script>
</body>
</html>
