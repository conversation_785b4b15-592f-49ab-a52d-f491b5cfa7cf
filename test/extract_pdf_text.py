#!/usr/bin/env python3
"""
简单的PDF文本提取脚本
"""

import sys
import json
import os

def extract_pdf_text(pdf_path):
    """提取PDF文本"""
    try:
        import PyPDF2
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            text_content = []
            page_count = len(pdf_reader.pages)
            
            for page_num, page in enumerate(pdf_reader.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_content.append(f"--- Page {page_num} ---\n{page_text}")
                except Exception as e:
                    text_content.append(f"--- Page {page_num} ---\n[Error extracting text from this page: {str(e)}]")
            
            extracted_text = "\n\n".join(text_content)
            
            if not extracted_text.strip():
                extracted_text = """PDF文档已上传，但无法提取文本内容。

可能的原因：
1. PDF是扫描版本（图片格式），需要OCR处理
2. PDF使用了特殊编码或加密
3. PDF内容主要是图像或图表

建议：
- 如果是扫描版PDF，请使用OCR工具转换为文本
- 或者手动复制粘贴PDF中的文本内容
- 确保PDF文件没有密码保护"""
            
            result = {
                "text": extracted_text,
                "page_count": page_count,
                "word_count": len(extracted_text.split()),
                "extraction_method": "PyPDF2",
                "success": True
            }
            
            return result
            
    except ImportError:
        result = {
            "text": """PyPDF2库未安装，无法自动提取PDF文本内容。

要启用PDF文本提取功能，请安装PyPDF2：
pip install PyPDF2

当前您可以：
1. 手动复制粘贴PDF中的文本内容
2. 使用在线PDF转文本工具
3. 在本地使用PDF阅读器复制文本""",
            "page_count": 1,
            "word_count": 50,
            "extraction_method": "no-pypdf2",
            "success": False,
            "error": "PyPDF2 not installed"
        }
        return result
        
    except Exception as e:
        result = {
            "text": f"""PDF处理过程中发生错误：{str(e)}

请尝试：
1. 确保PDF文件没有损坏
2. 检查PDF文件大小（建议小于10MB）
3. 如果问题持续，请手动复制粘贴文本内容

错误详情：{str(e)}""",
            "page_count": 1,
            "word_count": 30,
            "extraction_method": "error",
            "success": False,
            "error": str(e)
        }
        return result

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python extract_pdf_text.py <pdf_file_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    if not os.path.exists(pdf_path):
        print(json.dumps({
            "error": f"File not found: {pdf_path}",
            "success": False
        }))
        sys.exit(1)
    
    result = extract_pdf_text(pdf_path)
    print(json.dumps(result, ensure_ascii=False, indent=2))
