The fields of computer science and fiction writing are commonly used in today's society, and there is an important relationship between reading and writing and working with technology. These are two opposite fields; computer science is based more on knowledge in technology, logic, and mathematics while fiction is more based on imagination. However, both of these fields use creativity. This leads to the question- what are the differences in the creative mindset between the computer scientist and the fiction writer?
By using terms from <PERSON>'s reading such as domain, flow, the cognitive creative process, and the componential model of creativity, they will help differ the two fields and discover the thought and creative process for both. The term domain, which is a preexisting area of expertise, will be used to define one's specialty in computer science or fiction writing. The term flow, which is a state of mind where one is flooded with ideas and deeply concentrated in his/her work (as cited in Kaufman 36), will be used to determine whether individuals with a domain in computer science can experience this altered state of mind.
The cognitive creative process, created by <PERSON><PERSON>, uses the five stages: preparation, incubation, intimation, illumination, and verification to explain how one thinks creatively. In the preparation stage, one begins work on a problem. Incubation is where one can work on other tasks while his/her mind continues to think about the problem from the preparation stage. In the intimation stage, one realizes he/she is about to have a breakthrough. Hence, illumination is when one gets insight. Finally, verification is where one tests, develops, and uses his/her ideas (as cited in Kaufman 38). The cognitive creative process supports this paper because it is used and applied to computer science and fiction writing. When discovering this process for both domains, one can see the differences between the two.
The componential model of creativity, proposed by Amabile, states that domain-relevant skills, creativity-relevant skills, and task motivation are needed for creativity to occur. Domain-relevant skills consist of knowledge, technical skills, and specialized talent. Creativity-relevant skills consist of personal factors that are associated with creativity, such as self-discipline and risk-taking. Task motivation is simply being motivated toward the task at hand (as cited in Kaufman 43). By applying the componential model of creativity to both domains, it shows how each one is creative. As a result, the cognitive creative process and the componential model of creativity help identify the differences between the two creative mindsets.
Since these are two opposite fields, it can be difficult to find connections between the two in order to apply them to one another and create contrasts. Also, there is a controversy between the two: computer scientists help solve problems by using programs, and writing can improve one's psychological being and enable self expression. That being said, which domain is more beneficial to the world? 